apiVersion: v1
kind: ConfigMap
metadata:
  name: bol-core-configmap
  labels:
    app: bol
    cost-center: xxxx
    org: ID06
    vertical: BOL
data:
  frontend_url: ''
  standalone_url: ''
  backend_url: ''
  old_domain: 'unused.local'
  service_portal_url: ''
  company_registry_url: ''
  companies_url: ''
  bol_stamp_base_url: ''
  sp_framework_api_url: ''
  sp_framework_api_verify_tls: 'true'
  shared_cookie_domain: ''
  auth_data_caching_duration_in_seconds: '5'
  service_provider: 'id06'
  person_org_contract_type: user_account
  org_contract_type: customership
  verify_requests: 'true'
  bda_base_url: 'http://bol-data-api-core-service:8000'
  cqpoc_base_url: ''
  qvarn_base_url: ''
  qvarnlike_url_for_orgs: ''
  qvarnlike_url_for_cards: ''
  qvarnlike_url_for_persons: ''
  coresysapi_base_url: 'http://pimcore-web-service.pimcore.svc.cluster.local:8080'
  gluu_base_url: ''
  gluu_token_path: /oxauth/restv1/token
  gluu_authorize_path: /oxauth/restv1/authorize
  session_type: 'ext:redis'
  contract_api_base_url: ''
  user_account_api_base_url: ''
  session_cookie_domain: ''
  crontab: beta
  bulkimport_data_provider: ''
  # Revert to Sandbox report data provider once CS GGS testing is over.
  # report_data_provider: bolfak.statusreports.sandbox.Sandbox
  report_swedish_data_provider: ''
  report_foreign_data_provider: ''
  tax_data_provider: ''
  worker_process_count: ''
  session_cookie_secure: 'true'
  session_cookie_samesite: 'None'
  qvarn_threadpool_size: '20'
  log_level: ''
  test_api: ''
  sendgrid_sender: 'None' # REQUIRED: provided by the env patch
  default_storage: 'qvarn'
  autoaccount_request_package: 'None' # REQUIRED: provided by the env patch
  autoaccount_url: 'https://webservice.creditsafe.se/AutoAccount/AutoAccountService.asmx?WSDL'
  autoaccount_id06_gov_org_id: '**********'
  autoaccount_use_testing_org_gov_org_id: 'false'
  autoaccount_testing_org_gov_org_id: ''
  autoaccount_email: '<EMAIL>'
  change_user_details_url: ''
  cache_time_to_live: '15'
  cache_max_size: '5000'
  # Feature flags
  feature_flag_import_sole_traders: ''
  feature_flag_on_azure: 'true'
  feature_flag_company_registry: 'false'
  feature_flag_extended_report: 'true'
  feature_flag_pagination: 'true'
  feature_flag_visitors: 'true'
  feature_flag_project_report: 'true'
  feature_flag_celery_for_sendgrid: 'true'
  i18n_variant: 'id06'
  feature_flag_web_reports: 'false'
  feature_flag_stamp_workaround_one_site_at_a_time: 'true'
  feature_flag_pre_announcements: 'true'
  feature_flag_pa_form_checkbox_disabled: 'true'
  feature_flag_non_paed_suppliers: 'true'
  feature_flag_contract_api_creditsafe_contract: 'false'
  feature_flag_user_account_api: 'false'
  feature_flag_add_project_client: 'true'
  feature_flag_block_project_client: 'true'
  feature_flag_skip_pa_reg_step: 'false'
  feature_flag_create_and_activate_cs_accounts: 'true'
  feature_flag_person_id_for_project_users: 'true'
  feature_flag_core_mitt_id06: 'true'
  feature_flag_project_supplier_comments: 'true'
  feature_flag_dependency_request_cache: 'false'
  visitor_whitelist_org_ids: ''
  swagger_api: ''
  # Celery
  CELERY_TASK_DEFAULT_QUEUE: ''
  CELERY_TASK_DEFAULT_EXCHANGE: ''
  # BDA
  feature_flag_bda_client: 'true'
  feature_flag_bda_company_list: 'true'
  feature_flag_bda_project_suppliers: 'true'
  # Company Qvarn POC
  feature_flag_cqpoc_client: 'false'
  feature_flag_cqpoc_get_companies: 'false'
  feature_flag_lazy_qvarn_startup: 'true'
  feature_flag_require_creditsafe_contract: 'false'
  # Deprecated
  NEW_RELIC_ENVIRONMENT: ""
