- op: replace
  path: /data/gluu_base_url
  value: "https://auth-core.beta.id06.se"
- op: replace
  path: /data/frontend_url
  value: "https://mitt.beta.id06.se/"
- op: replace
  path: /data/standalone_url
  value: "https://bol.beta.id06.se/"
- op: replace
  path: /data/backend_url
  value: "https://bol.beta.id06.se/api/"
- op: replace
  path: /data/old_domain
  value: "bfbeta.id06.se"
- op: replace
  path: /data/report_swedish_data_provider
  value: "bolfak.statusreports.creditsafe.CreditsafeV2"
- op: replace
  path: /data/report_foreign_data_provider
  value: "bolfak.statusreports.creditsafe_connect_core.CreditsafeConnectCoreReportProvider"
- op: replace
  path: /data/bol_stamp_base_url
  value: "https://stampdata-api.beta.vaultit.org"
- op: replace
  path: /data/shared_cookie_domain
  value: ".id06.se"
- op: replace
  path: /data/bulkimport_data_provider
  value: "bolfak.statusreports.creditsafe.CreditsafeInfoProvider"
- op: replace
  path: /data/worker_process_count
  value: "8"
- op: replace
  path: /data/log_level
  value: "INFO"
- op: replace
  path: /data/test_api
  value: "false"
- op: replace
  path: /data/sendgrid_sender
  value: ID06 beta <<EMAIL>>
- op: replace
  path: /data/autoaccount_request_package
  value: ID06_TEST_AA
- op: replace
  path: /data/autoaccount_use_testing_org_gov_org_id
  value: "true"
- op: replace
  path: /data/autoaccount_testing_org_gov_org_id
  value: "**********"
# Feature flags
- op: replace
  path: /data/feature_flag_import_sole_traders
  value: "true"
- op: replace
  path: /data/swagger_api
  value: "false"
- op: replace
  path: /data/feature_flag_skip_pa_reg_step
  value: "true"
- op: replace
  path: /data/feature_flag_project_supplier_comments
  value: "false"
# Visitor whitelist
- op: replace
  path: /data/visitor_whitelist_org_ids
  value: ""
# Celery
- op: replace
  path: /data/CELERY_TASK_DEFAULT_QUEUE
  value: "celery-que-bol-core-beta"
- op: replace
  path: /data/CELERY_TASK_DEFAULT_EXCHANGE
  value: "celery-exc-bol-beta"
# Deprecated
- op: replace
  path: /data/NEW_RELIC_ENVIRONMENT
  value: "bolswe-beta"
