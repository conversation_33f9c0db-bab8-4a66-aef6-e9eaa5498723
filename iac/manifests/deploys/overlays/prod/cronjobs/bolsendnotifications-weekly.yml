# Notifications should run after statusreports scripts.
apiVersion: batch/v1
kind: CronJob
metadata:
  name: bol-sendnotifications-weekly-cronjob
  labels:
    cost-center: xxxx
    env: prod
    org: ID06
    vertical: BOL
spec:
  schedule: "00  4    * * 1"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      activeDeadlineSeconds: 21600 # 6 hours
      template:
        spec:
          restartPolicy: OnFailure
          nodeSelector:
            agentpool: npuser01
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            fsGroup: 1000
          containers:
            - name: bol-sendnotifications-weekly-cronjob
              image: crvaultitprod.azurecr.io/bol/bol
              imagePullPolicy: Always
              command:
                - /bin/sh
                - -c
                - ./run.sh; bolsendnotifications -c /etc/bolfak/notifications.cfg --weekly
              resources:
                requests:
                  memory: "256Mi"
                limits:
                  memory: "2G"
              env:
                - name: app
                  value: bolagsdeklaration
              envFrom:
                - secretRef:
                    name: bol-creds
                - configMapRef:
                    name: bol-configmap
              securityContext:
                allowPrivilegeEscalation: false
