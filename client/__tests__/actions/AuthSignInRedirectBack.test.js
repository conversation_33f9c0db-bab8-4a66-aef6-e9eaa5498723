/* eslint-env jest */

import ActionTypes from '../../app/actions/ActionTypes';
import dispatcher from '../../app/Dispatcher';
import getAPIClient from '../../app/helpers/ApiClient';
import authSignInFailed from '../../app/actions/actionCreators/AuthSignInFailed';
import { checkBolPermission, getOrgRepresentation } from '../../app/helpers/Authentication';
import { featureActive } from '../../app/helpers/FeatureFlags';
import window from '../../__mocks__/window';
import authSignInRedirectBack, {
  getIndexRedirectURL, fetchAuthenticationInformation
} from '../../app/actions/actionCreators/AuthSignInRedirectBack';
import authorizationFailed from '../../app/actions/actionCreators/AuthorizationFailed';

jest.mock('../../app/Dispatcher', () => {
  const callbacks = [];
  let isDispatching = false;

  return {
    __esModule: true,
    default: {
      register: jest.fn(callback => callbacks.push(callback)),
      dispatch: jest.fn(action => {
        isDispatching = true;
        callbacks.forEach(callback => callback(action));
        isDispatching = false;
      }),
      isDispatching: jest.fn(() => isDispatching),
    },
  };
});

jest.mock('../../__mocks__/window', () => ({
  ...jest.requireActual('../../__mocks__/window'),
}));

// Default ApiClient mock with ok response
jest.mock('../../app/helpers/ApiClient', () => {
  const mockAPIClient = {
    get: jest.fn().mockResolvedValue({ data: { ok: true } }),
    post: jest.fn().mockResolvedValue({ data: { ok: true } }),
  };
  return jest.fn(() => mockAPIClient);
});

jest.mock('../../app/actions/actionCreators/AuthSignIn', () => jest.fn());
jest.mock('../../app/actions/actionCreators/AuthCompleteSignIn', () => jest.fn());
jest.mock('../../app/actions/actionCreators/AuthSignInFailed', () => jest.fn());
jest.mock('../../app/actions/actionCreators/AuthorizationFailed', () => jest.fn());

jest.mock('../../app/helpers/Authentication', () => ({
  checkBolPermission: jest.fn(),
  getOrgRepresentation: jest.fn(),
  checkActiveCreditsafeAccount: jest.fn(),
  checkPendingCreditsafeAccount: jest.fn(),
  checkCreditsafeContract: jest.fn(),
  chooseActiveOrganisation: jest.fn(),
}));

jest.mock('../../app/helpers/FeatureFlags', () => ({
  featureActive: jest.fn(),
}));

jest.mock('../../app/Config', () => ({
  noPermissionURL: 'https://example.com/no-permission',
}));

describe('authSignInRedirectBack', () => {
  let consoleLogSpy;
  let consoleErrorSpy;

  beforeEach(() => {
    window.retryAuthInfo = false;
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleLogSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });

  it('should dispatch AUTH_SIGNIN_REDIRECT_BACK action', async () => {
    const data = {
      topMenuParameters: {
        selectedRepresentedOrganizationId: 'orgid',
        representationsOpenedInAdminMode: [],
      },
      userProfile: {
        allRepresentationCodes: [],
        fullName: '',
        username: '',
      },
    };
    const mockAPIClient = {
      get: jest.fn().mockResolvedValue({ data: data }),
    };
    // Override the getAPIClient function with the custom mockAPIClient
    getAPIClient.mockReturnValue(mockAPIClient);
    checkBolPermission.mockReturnValue(true);
    getOrgRepresentation.mockReturnValue({
      organizationId: 'orgid',
      organizationName: 'org',
      globalPermissions: [],
      userRole: 'main',
    });
    await authSignInRedirectBack(true, { location: { pathname: '/' } });
    expect(document.location.hash).toBe('#/projects');
    expect(dispatcher.dispatch).toHaveBeenCalledWith({
      type: ActionTypes.AUTH_SIGNIN_REDIRECT_BACK,
    });
  });

  it('should handle error when fetching authentication info', async () => {
    const error = new Error('Failed to fetch authentication info');
    const mockAPIClient = {
      get: jest.fn().mockRejectedValue(error),
    };
    // Override the getAPIClient function with the custom mockAPIClient
    getAPIClient.mockReturnValue(mockAPIClient);

    await authSignInRedirectBack(true, { location: { pathname: '/' } });
    expect(authSignInFailed).toHaveBeenCalledTimes(1);
  });
});

describe('getIndexRedirectURL', () => {
  beforeEach(() => {
    jest.resetModules();
  });

  // eslint-disable-next-line max-len
  it('should return "/activate-cs-account" when "create_and_activate_cs_accounts" is active, hasPendingCreditsafeAccount is true, and hasActiveCreditsafeAccount is false', () => {
    featureActive.mockImplementation(f => f === 'create_and_activate_cs_accounts');

    const result = getIndexRedirectURL(true, false);
    expect(result).toBe('/activate-cs-account');
  });

  // eslint-disable-next-line max-len
  it('should return "/create-cs-account" when "create_and_activate_cs_accounts" is active and hasActiveCreditsafeAccount is false', () => {
    featureActive.mockImplementation(f => f === 'create_and_activate_cs_accounts');

    const result = getIndexRedirectURL(false, false);
    expect(result).toBe('/create-cs-account');
  });

  it('should return "/search" when "search" is active', () => {
    featureActive.mockImplementation(f => f === 'search');

    const result = getIndexRedirectURL(false, true);
    expect(result).toBe('/search');
  });

  // eslint-disable-next-line max-len
  it('should return "/projects" when neither "create_and_activate_cs_accounts" nor "search" is active', () => {
    featureActive.mockImplementation(() => false);

    const result = getIndexRedirectURL(false, true);
    expect(result).toBe('/projects');
  });
});

describe('fetchAuthenticationInformation', () => {
  let consoleLogSpy;
  let consoleErrorSpy;

  beforeEach(() => {
    jest.resetModules();
    featureActive.mockImplementation(f => f === 'core_mitt_id06');
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleLogSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });

  it('should handle errors by using the authorizationFailed action', async () => {
    const error = new Error({data: {en: 'error detail'}});
    const mockAPIClient = {
      get: jest.fn().mockResolvedValue({ data: { ok: true } }),
      post: jest.fn().mockRejectedValue(error),
    };
    // Override the getAPIClient function with the custom mockAPIClient
    getAPIClient.mockReturnValue(mockAPIClient);

    await fetchAuthenticationInformation('ig');
    expect(authorizationFailed).toHaveBeenCalledWith(error);
  });
});
