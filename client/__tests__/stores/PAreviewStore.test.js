/* eslint-env jest */

import dispatcher from '../../app/Dispatcher';
import ActionTypes from '../../app/actions/ActionTypes';
import PAreviewStore from '../../app/stores/PAreviewStore';
import updatePAreviewFail from '../../app/actions/actionCreators/UpdatePAreviewFail';
import updatePAreviewSuccess from '../../app/actions/actionCreators/UpdatePAreviewSuccess';

jest.mock('../../app/helpers/ApiClient', () => {
  const mockAPIClient = {
    post: jest.fn().mockResolvedValue({ data: { ok: true } }),
  };
  return jest.fn(() => mockAPIClient);
});

jest.mock('../../app/actions/actionCreators/UpdatePAreviewFail', () => jest.fn());
jest.mock('../../app/actions/actionCreators/UpdatePAreviewSuccess', () => jest.fn());

describe('PAreviewStore', () => {
  let store;

  beforeEach(() => {
    store = new PAreviewStore(dispatcher);
    updatePAreviewFail.mockClear();
    updatePAreviewSuccess.mockClear();
  });

  it('sets initial state', () => {
    const state = store.getInitialState();
    expect(state).toEqual({
      status: null,
      update_in_progress: false,
      error_message_id: null,
    });
  });

  it('handles PA_VIEW_OPEN action and resets state', () => {
    // Set up a modified state with all properties changed
    const state = {
      status: 'approved',
      update_in_progress: true,
      error_message_id: 'some-error-message',
    };

    // Call the action handler
    store[ActionTypes.PA_VIEW_OPEN](state);

    // Verify state is reset to initial state
    expect(state).toEqual(store.getInitialState());
  });
});
