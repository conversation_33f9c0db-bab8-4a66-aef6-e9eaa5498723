/* eslint-env jest */

import dispatcher from '../../app/Dispatcher';
import ActionTypes from '../../app/actions/ActionTypes';
import CommentsStore from '../../app/stores/CommentsStore';

describe('CommentsStore', () => {
  let store;

  beforeEach(() => {
    store = new CommentsStore(dispatcher);
  });

  it('sets initial state', () => {
    const state = store.getInitialState();
    expect(state).toEqual({
      supplier_id: null,
      comments_loading: false,
      comments_loaded: false,
      comments_failed: false,
      comments: [],
      comment_map: {},
      comment_delete_id: null,
      comment_delete_failed: false,
      comment_add_in_progress: false,
      comment_add_errors: null,
      has_unsaved_comment_draft: false,
    });
  });

  it('handles COMPANY_PROJECTS_COMMENTS_CLEAR action and resets state', () => {
    // Set up a modified state with various properties changed
    const state = {
      supplier_id: 'test-supplier-123',
      comments_loading: true,
      comments_loaded: true,
      comments_failed: false,
      comments: [
        { id: 1, text: 'test comment 1' },
        { id: 2, text: 'test comment 2' },
      ],
      comment_map: {
        1: { id: 1, text: 'test comment 1' },
        2: { id: 2, text: 'test comment 2' },
      },
      comment_delete_id: 1,
      comment_delete_failed: true,
      comment_add_in_progress: true,
      comment_add_errors: { error: 'some error' },
      has_unsaved_comment_draft: true,
    };

    // Call the action handler
    store[ActionTypes.COMPANY_PROJECTS_COMMENTS_CLEAR](state);

    // Verify state is reset to initial state
    expect(state).toEqual(store.getInitialState());
  });

  it('handles DETAILS_VIEW_CLOSE action and resets state', () => {
    // Set up a modified state
    const state = {
      supplier_id: 'another-supplier',
      comments_loading: true,
      comments_loaded: true,
      comments: [{ id: 3, text: 'another comment' }],
      comment_map: { 3: { id: 3, text: 'another comment' } },
      comment_add_in_progress: false,
      comment_add_errors: null,
      has_unsaved_comment_draft: false,
    };

    // Call the action handler
    store[ActionTypes.DETAILS_VIEW_CLOSE](state);

    // Verify state is reset to initial state
    expect(state).toEqual(store.getInitialState());
  });
});
