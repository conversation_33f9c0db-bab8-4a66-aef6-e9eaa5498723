/* eslint-env jest */

// Mock dependencies that cause circular import issues
jest.mock('../../app/actions/actionCreators/ProjectTreeLoaded', () => jest.fn());
jest.mock('../../app/actions/actionCreators/ProjectTreeFailedToLoad', () => jest.fn());
jest.mock('../../app/actions/actionCreators/AddSubcontractorsSaveSuccess', () => jest.fn());
jest.mock('../../app/actions/actionCreators/AddSubcontractorsSaveFailure', () => jest.fn());
jest.mock('../../app/actions/actionCreators/SubcontractorFormCloseClicked', () => jest.fn());
jest.mock('../../app/actions/actionCreators/AddSubcontractorsClearFindClicked', () => jest.fn());
jest.mock('../../app/actions/actionCreators/GetProjectUsers', () => jest.fn());
jest.mock('../../app/helpers/ApiClient', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
  })),
}));
jest.mock('../../app/helpers/ProjectTree', () => ({
  updateSupplier: jest.fn(),
}));

import dispatcher from '../../app/Dispatcher';
import ActionTypes from '../../app/actions/ActionTypes';
import ProjectViewStore from '../../app/stores/ProjectViewStore';

describe('ProjectViewStore', () => {
  let store;

  beforeEach(() => {
    store = new ProjectViewStore(dispatcher);
  });

  describe('UPDATE_SUPPLIER_NODE action', () => {
    it('should not crash when project_tree is null', () => {
      // This test covers the fix from commit BOL-6662
      const state = {
        ...store.getInitialState(),
        project_tree: null,
      };

      const action = {
        supplierId: 'test-supplier-123',
        patch: { status: 'updated' },
      };

      // This should not throw an error
      expect(() => {
        store[ActionTypes.UPDATE_SUPPLIER_NODE](state, action);
      }).not.toThrow();

      // State should remain unchanged when project_tree is null
      expect(state.project_tree).toBeNull();
    });

    it('should update supplier node when project_tree exists', () => {
      const { updateSupplier } = require('../../app/helpers/ProjectTree');
      const mockRoot = {
        id: 'root',
        suppliers: [
          { id: 'supplier-1', name: 'Supplier 1' },
          { id: 'supplier-2', name: 'Supplier 2' },
        ],
      };
      const updatedRoot = {
        ...mockRoot,
        suppliers: [
          { id: 'supplier-1', name: 'Supplier 1', status: 'updated' },
          { id: 'supplier-2', name: 'Supplier 2' },
        ],
      };
      updateSupplier.mockReturnValue(updatedRoot);
      const state = {
        ...store.getInitialState(),
        project_tree: {
          root: mockRoot,
        },
      };
      const action = {
        supplierId: 'supplier-1',
        patch: { status: 'updated' },
      };

      store[ActionTypes.UPDATE_SUPPLIER_NODE](state, action);

      expect(updateSupplier).toHaveBeenCalledWith(
        mockRoot,
        'supplier-1',
        { status: 'updated' }
      );

      expect(state.project_tree.root).toEqual(updatedRoot);
    });

    it('should handle undefined project_tree gracefully', () => {
      const state = {
        ...store.getInitialState(),
      };
      delete state.project_tree;
      
      const action = {
        supplierId: 'test-supplier-123',
        patch: { status: 'updated' },
      };
      expect(() => {
        store[ActionTypes.UPDATE_SUPPLIER_NODE](state, action);
      }).not.toThrow();

      // State should remain unchanged
      expect(state.project_tree).toBeUndefined();
    });
  });
});
