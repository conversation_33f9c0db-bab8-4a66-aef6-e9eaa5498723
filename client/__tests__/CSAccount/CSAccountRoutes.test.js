/* eslint-env jest */
import React from 'react';
import { render } from '@testing-library/react';
import BolagsfaktaRoutes from '../../app/legacy/Routes';
import { MemoryRouter } from 'react-router-dom';

jest.mock('../../app/actions/actionCreators/AuthSignInRedirectBack', () => jest.fn());

jest.mock('../../app/components/CSAccount/CSAccountCreatePage', () => () => (
  <div>Mocked CSAccountCreatePage</div>
));

jest.mock('../../app/components/CSAccount/CSAccountActivatePage', () => () => (
  <div>Mocked CSAccountActivatePage</div>
));

// eslint-disable-next-line react/prop-types
jest.mock('../../app/components/BasePage', () => ({ children }) =>
  <div>{children}</div>);

describe('CSAccountRoutes test', () => {
  it.skip('the /create-cs-account route should exist and open CSAccountCreatePage', () => {
    const { getByText } = render(
      <MemoryRouter initialEntries={['/create-cs-account']}>
        <BolagsfaktaRoutes />
      </MemoryRouter>,
    );
    expect(getByText('Mocked CSAccountCreatePage')).toBeInTheDocument();
  });
 
  it.skip('the /activate-cs-account route should exist and open CSAccountActivatePage', () => {
    const { getByText } = render(
      <MemoryRouter initialEntries={['/activate-cs-account']}>
        <BolagsfaktaRoutes />
      </MemoryRouter>,
    );
    expect(getByText('Mocked CSAccountActivatePage')).toBeInTheDocument();
  });
});
