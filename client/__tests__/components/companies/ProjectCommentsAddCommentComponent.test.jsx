/* global jest, describe, it, expect, beforeEach */
import React from 'react';
import { IntlProvider } from 'react-intl';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ProjectCommentsAddCommentComponent from
  '../../../app/components/companies/ProjectCommentsAddCommentComponent';
import { commentsStore } from '../../../app/stores/Stores';
import companyProjectCommentsAdd from
  '../../../app/actions/actionCreators/CompanyProjectCommentsAdd';
import companyProjectCommentsSetDraftState 
  from '../../../app/actions/actionCreators/CompanyProjectCommentsSetDraftState';
import companyProjectCommentsSetHasUnsavedEdits 
  from '../../../app/actions/actionCreators/CompanyProjectCommentsSetHasUnsavedEdits';

// Mock the StoreSubscription
let mockActivate;
let mockDeactivate;

jest.mock('../../../app/helpers/StoreSubscription', () =>
  jest.fn().mockImplementation(() => {
    mockActivate = jest.fn();
    mockDeactivate = jest.fn();
    return {
      activate: mockActivate,
      deactivate: mockDeactivate,
    };
  })
);

jest.mock('../../../app/actions/actionCreators/CompanyProjectCommentsAdd', () =>
  jest.fn().mockResolvedValue(true)
);

jest.mock('../../../app/actions/actionCreators/CompanyProjectCommentsSetDraftState', () =>
  jest.fn().mockResolvedValue()
);

jest.mock('../../../app/actions/actionCreators/CompanyProjectCommentsSetHasUnsavedEdits', () =>
  jest.fn().mockResolvedValue()
);

describe('ProjectCommentsAddCommentComponent', () => {
  const mockComment = {
    loading: false,
    editing: false,
    errors: [],
    data: {
      id: '1',
      author: 'Test User',
      comment: 'This is a test comment',
      created_timestamp: '2023-01-01T12:00:00Z',
      updated_timestamp: null,
      deleted_timestamp: null,
      is_read: false,
      permissions: ['update', 'delete'],
    },
  };

  const renderComponent = (state = {}, props = {}, locale = 'en') => {
    const defaultState = {
      supplier_id: 'test-supplier-123',
      comment_add_in_progress: false,
      comment_add_errors: null,
      has_unsaved_comment_draft: false,
      ...state,
    };
    jest.clearAllMocks();

    commentsStore.getState = jest.fn(() => defaultState);

    const utils = render(
      <IntlProvider locale={locale}>
        <ProjectCommentsAddCommentComponent {...props} />
      </IntlProvider>
    );

    return {
      ...utils,
      textarea: screen.getByRole('textbox'),
      getSaveButton: () => screen.queryByRole('button', { name: /save/i }),
      getCancelButton: () => document.querySelector('a[href="#"]'),
      getHelpText: () => screen.getByText(/comments are only visible to the main contractor/i),
      getPlaceholder: () => screen.queryByPlaceholderText(/write a comment about the supplier/i),
      rerender: (newProps) => {
        utils.rerender(
          <IntlProvider locale={locale}>
            <ProjectCommentsAddCommentComponent {...newProps} />
          </IntlProvider>
        );
      },
    };
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with initial state', () => {
    const { textarea, getHelpText, getPlaceholder } = renderComponent();

    expect(textarea).toBeInTheDocument();
    expect(getPlaceholder()).toBeInTheDocument();
    expect(getHelpText()).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /save/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /cancel/i })).not.toBeInTheDocument();
  });

  it('shows save and cancel buttons when textarea is focused', () => {
    const { textarea } = renderComponent();

    fireEvent.focus(textarea);

    expect(screen.getByRole('button', { name: /save/i })).toBeInTheDocument();
    expect(document.querySelector('a[href="#"]')).toHaveTextContent('cancel');
  });

  it('should enable and disable save button when content is changed', () => {
    const { textarea } = renderComponent();

    fireEvent.focus(textarea);
    const saveButton = screen.getByRole('button', { name: /save/i });

    // Initially disabled
    expect(saveButton).toBeDisabled();

    // Type some text
    fireEvent.input(textarea, { target: { value: 'Test comment' } });

    // Should use store to enable the button
    expect(companyProjectCommentsSetDraftState).toHaveBeenCalledWith(true);

    // Clear the text
    fireEvent.input(textarea, { target: { value: '   ' } });
    // Should use store to disable the button
    expect(companyProjectCommentsSetDraftState).toHaveBeenCalledWith(true);
  });

  it('calls companyProjectCommentsAdd with correct parameters on form submit', () => {
    const { textarea } = renderComponent({
      has_unsaved_comment_draft: true,
    });
    const testComment = 'This is a test comment';

    fireEvent.focus(textarea);
    fireEvent.input(textarea, { target: { value: testComment } });
    fireEvent.click(screen.getByRole('button', { name: /save/i }));
    expect(companyProjectCommentsAdd).toHaveBeenCalledWith('test-supplier-123', testComment.trim());
  });

  it('clears the input after successful submission', async () => {
    const { textarea } = renderComponent({
      has_unsaved_comment_draft: true,
    });
    const testComment = 'This is a test comment';

    fireEvent.focus(textarea);
    fireEvent.input(textarea, { target: { value: testComment } });
    fireEvent.click(screen.getByRole('button', { name: /save/i }));

    await waitFor(() => {
      expect(textarea.value).toBe('');
    });
  });

  it('clears the input when cancel is clicked', () => {
    const { textarea } = renderComponent();
    const testComment = 'This is a test comment';

    fireEvent.focus(textarea);
    fireEvent.input(textarea, { target: { value: testComment } });

    // Verify text is there
    expect(textarea.value).toBe(testComment);

    // Click cancel
    fireEvent.click(screen.getByRole('button', { name: /cancel/i }));

    // Text should be cleared
    expect(textarea.value).toBe('');
  });

  it('disables the textarea and save button when comment_add_in_progress is true', () => {
    const { textarea } = renderComponent({
      comment_add_in_progress: true,
    });

    fireEvent.focus(textarea);
    fireEvent.input(textarea, { target: { value: 'Test comment' } });

    const saveButton = screen.getByRole('button', { name: /save/i });

    expect(textarea).toBeDisabled();
    expect(saveButton).toBeDisabled();
  });

  it('displays error messages when comment_add_errors is present', () => {
    const errorMessage = 'This is an error message';
    renderComponent({
      comment_add_errors: [{ id: 'test.error', defaultMessage: errorMessage }],
    });

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('handles store subscription on mount and unmount', () => {
    const { unmount } = renderComponent();

    // Check if StoreSubscription was created and activated
    expect(mockActivate).toHaveBeenCalledTimes(1);

    // Check if deactivate is called on unmount
    unmount();
    expect(mockDeactivate).toHaveBeenCalledTimes(1);
  });

  it('disables the submit button when in edit mode and input is unchanged', () => {
    const { textarea } = renderComponent(
      {
        comment_map: {
          [mockComment.data.id]: {
            ...mockComment,
            editing: true,
          },
        },
      },
      { editCommentId: mockComment.data.id, editCommentText: mockComment.data.comment }
    );

    fireEvent.change(textarea, { target: { value: mockComment.data.comment + ' change' } });
    fireEvent.change(textarea, { target: { value: mockComment.data.comment } });
    expect(companyProjectCommentsSetHasUnsavedEdits).toHaveBeenCalledWith(mockComment.data.id, false);
  });

  it('enables the submit button when in edit mode and input is unchanged', () => {
    const { textarea } = renderComponent(
      {
        comment_map: {
          [mockComment.data.id]: {
            ...mockComment,
            editing: true,
          },
        },
      },
      { editCommentId: mockComment.data.id, editCommentText: mockComment.data.comment }
    );

    fireEvent.input(textarea, { target: { value: mockComment.data.comment + ' a' } });
    expect(companyProjectCommentsSetHasUnsavedEdits).toHaveBeenCalledWith(mockComment.data.id, true);
  });

  it('disables the textarea and submit button in edit mode when comment is loading', () => {
    renderComponent(
      {
        comment_map: {
          [mockComment.data.id]: {
            ...mockComment,
            editing: true,
            loading: true,
          },
        },
      },
      { editCommentId: mockComment.data.id, editCommentText: mockComment.data.comment }
    );

    const saveButton = screen.getByRole('button', { name: /save/i });
    const textarea = screen.getByRole('textbox');

    expect(saveButton).toBeDisabled();
    expect(textarea).toBeDisabled();
  });
});
