/* global jest, describe, it, expect, beforeEach */
import React from 'react';
import { IntlProvider } from 'react-intl';
import { render, screen, fireEvent } from '@testing-library/react';
import ProjectTreeNodeComponent from '../../../app/components/projects/ProjectTreeNodeComponent';
import en from '../../../app/localizations/en.json';

// Mock actions
jest.mock('../../../app/actions/actionCreators/CompanyDetailsTreeMenuClicked', () => jest.fn());
jest.mock('../../../app/actions/actionCreators/AddSubcontractorsMenuClicked', () => jest.fn());
jest.mock('../../../app/actions/actionCreators/SubcontractorEditMenuClicked', () => jest.fn());
jest.mock('../../../app/actions/actionCreators/MoveSupplierMenuClicked', () => jest.fn());
jest.mock('../../../app/actions/actionCreators/PlaceSupplierMenuClicked', () => jest.fn());
jest.mock('../../../app/actions/actionCreators/ProjectTreeCancelCurrentMove', () => jest.fn());
jest.mock('../../../app/actions/actionCreators/ProjectTreeUndoMove', () => jest.fn());
jest.mock('../../../app/actions/actionCreators/ProjectTreeOpenReport', () => jest.fn());

// Mock components
jest.mock('../../../app/components/shared/StatusIconComponent', () => {
  return function MockStatusIconComponent({ status }) {
    return <span data-testid="status-icon">{status}</span>;
  };
});

jest.mock('../../../app/components/shared/VisitorIconComponent', () => {
  return function MockVisitorIconComponent() {
    return <span data-testid="visitor-icon">Visitor</span>;
  };
});

jest.mock('../../../app/components/shared/IdCardIconComponent', () => {
  return function MockIdCardIconComponent() {
    return <span data-testid="id-card-icon">ID Card</span>;
  };
});

jest.mock('../../../app/components/shared/CommentIconComponent', () => {
  return function MockCommentIconComponent({ has_comment, has_unread_comment }) {
    if (!has_comment) return null;
    const iconClass = has_unread_comment ? 'fa-comments' : 'fa-comments-o';
    const style = has_unread_comment ? { color: '#32A1D1' } : { color: '#999999' };
    return (
      <i
        data-testid="comment-icon"
        className={`fa ${iconClass}`}
        style={style}
        aria-label={has_unread_comment ? 'Unread comments' : 'Read comments'}
      />
    );
  };
});

jest.mock('../../../app/components/projects/NodeMenuComponent', () => {
  return function MockNodeMenuComponent({ children }) {
    return <div data-testid="node-menu">{children}</div>;
  };
});

jest.mock('../../../app/components/projects/NodeMenuItem', () => {
  return ({ visible, className, onClick, children }) =>
    visible ? (
      <div data-testid={className} className={className} onClick={onClick}>
        {children}
      </div>
    ) : null;
});

// Mock helpers
jest.mock('../../../app/helpers/ProjectTree', () => ({
  countChildren: jest.fn(() => 0),
}));

jest.mock('../../../app/helpers/EventHandlers', () => ({
  stopPropagation: jest.fn(e => e),
}));

// Import mocked actions for assertions
import companyDetailsTreeMenuClicked
  from '../../../app/actions/actionCreators/CompanyDetailsTreeMenuClicked';

describe('ProjectTreeNodeComponent', () => {
  const mockIntl = {
    formatMessage: jest.fn(msg => {
      if (msg.id === 'relatedSuppliers.unreadCommentAriaLabel') {
        return 'Unread comments';
      }
      if (msg.id === 'relatedSuppliers.readCommentAriaLabel') {
        return 'Read comments';
      }
      return msg.defaultMessage || msg.id;
    }),
    locale: 'en',
  };

  const mockProps = {
    id: 'test-node-1',
    name: 'Test Company',
    companyId: 'company-123',
    supplierId: 'supplier-456',
    supplierRev: 'rev-789',
    supplierType: 'linked',
    depth: 0,
    status: 'ok',
    govOrgId: '**********',
    vatNumber: 'SE123456789',
    country: 'SE',
    role: 'supplier',
    contractType: 'consulting',
    contractStartDate: '2024-01-01',
    contractEndDate: '2024-12-31',
    contractWorkAreas: ['other'],
    isOneManCompany: false,
    hasCollectiveAgreement: true,
    collectiveAgreementName: 'Test Agreement',
    contacts: [
      {
        supplier_contact_name: 'John Doe',
        supplier_contact_email: '<EMAIL>',
        is_person_registered: true,
      },
    ],
    childrenStatusCounts: {},
    worstSubtreeStatus: 'ok',
    firstVisited: '2024-01-01',
    lastVisited: '2024-01-15',
    reportAvailable: true,
    hasStatusIcon: true,
    hasAddAction: false,
    hasEditAction: true,
    hasMoveAction: false,
    hasPlaceAction: false,
    hasCancelAction: false,
    hasUndoAction: false,
    hasPreannounceAction: false,
    preannouncementId: null,
    hasCreateNewPreannouncementAction: false,
    hasDetailsAction: true,
    hasComment: false,
    hasUnreadComment: false,
    hasCommentAction: false,
    supplierPermissions: ['view_supplier', 'edit_supplier'],
    intl: mockIntl,
  };

  const renderComponent = (props = {}) => {
    const mergedProps = { ...mockProps, ...props };
    return render(
      <IntlProvider locale="en" messages={en}>
        <ProjectTreeNodeComponent {...mergedProps} />
      </IntlProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render without crashing', () => {
      const { container } = renderComponent();
      expect(container).toBeInTheDocument();
    });

    it('should render company name', () => {
      renderComponent();
      expect(screen.getByText('Test Company')).toBeInTheDocument();
    });

    it('should render status icon when hasStatusIcon is true', () => {
      renderComponent();
      expect(screen.getByTestId('status-icon')).toBeInTheDocument();
    });

    it('should not render status icon when hasStatusIcon is false', () => {
      renderComponent({ hasStatusIcon: false });
      expect(screen.queryByTestId('status-icon')).not.toBeInTheDocument();
    });

    it('should render visitor icon for visitor supplier type', () => {
      renderComponent({ supplierType: 'visitor', status: null });
      expect(screen.getByTestId('visitor-icon')).toBeInTheDocument();
    });
  });

  describe('Comments Icon Functionality', () => {
    it('should not render comments icon when hasComment is false', () => {
      renderComponent({ hasComment: false });
      expect(screen.queryByTestId('comments-icon')).not.toBeInTheDocument();
    });

    it('should render comments icon when hasComment is true', () => {
      renderComponent({ hasComment: true });
      const commentsIcon = screen.getByTestId('comments-icon');
      expect(commentsIcon).toBeInTheDocument();
      expect(commentsIcon).toHaveStyle({ cursor: 'pointer' });

      // Check that the CommentIconComponent is rendered inside
      const commentIcon = screen.getByTestId('comment-icon');
      expect(commentIcon).toBeInTheDocument();
      expect(commentIcon).toHaveClass('fa-comments-o');
    });

    it('should apply blue color style when hasUnreadComment is true', () => {
      renderComponent({ hasComment: true, hasUnreadComment: true });
      const commentIcon = screen.getByTestId('comment-icon');
      expect(commentIcon).toHaveStyle({ color: '#32A1D1' });
    });

    it('should apply gray color style when hasUnreadComment is false', () => {
      renderComponent({ hasComment: true, hasUnreadComment: false });
      const commentIcon = screen.getByTestId('comment-icon');
      expect(commentIcon).toHaveStyle({ color: '#999999' });
      expect(commentIcon).toHaveClass('fa-comments-o');
    });

    it('should have correct ARIA label for read comments', () => {
      renderComponent({ hasComment: true, hasUnreadComment: false });
      const commentIcon = screen.getByTestId('comment-icon');
      expect(commentIcon).toHaveAttribute('aria-label', 'Read comments');
    });

    it('should have correct ARIA label for unread comments', () => {
      renderComponent({ hasComment: true, hasUnreadComment: true });
      const commentIcon = screen.getByTestId('comment-icon');
      expect(commentIcon).toHaveAttribute('aria-label', 'Unread comments');
    });

    it('should have clickable styling', () => {
      renderComponent({ hasComment: true });
      const commentsIcon = screen.getByTestId('comments-icon');
      expect(commentsIcon).toHaveStyle({ cursor: 'pointer' });
    });

    it('should call companyDetailsTreeMenuClicked when comments icon wrapper is clicked', () => {
      renderComponent({ hasComment: true });
      const commentsIconWrapper = screen.getByTestId('comments-icon');
      fireEvent.click(commentsIconWrapper);

      expect(companyDetailsTreeMenuClicked).toHaveBeenCalledWith(
        expect.any(Object), // contextBlockTargetRef
        'company-123', // companyId
        'Test Company', // name
        'supplier-456' // supplierId
      );
    });

    it('should prevent event propagation when comments icon wrapper is clicked', () => {
      renderComponent({ hasComment: true });

      // Mock the stopPropagation function
      const { stopPropagation } = require('../../../app/helpers/EventHandlers');
      stopPropagation.mockImplementation(e => e);

      const commentsIconWrapper = screen.getByTestId('comments-icon');
      fireEvent.click(commentsIconWrapper);

      expect(stopPropagation).toHaveBeenCalled();
    });
  });

  describe('Comments Icon Positioning', () => {
    it('should render comments icon after company name with spacing', () => {
      renderComponent({ hasComment: true });
      const nodeTitle = screen.getByText('Test Company').closest('.node-title');

      // Check that the spacing element exists
      const spacingElement = nodeTitle.querySelector('span[style*="margin-left"]');
      expect(spacingElement).toBeInTheDocument();
      expect(spacingElement).toHaveStyle({ marginLeft: '8px' });

      // Check that comments icon comes after the spacing
      const commentsIcon = screen.getByTestId('comments-icon');
      expect(nodeTitle.contains(commentsIcon)).toBe(true);
    });

    it('should not render spacing element when hasComment is false', () => {
      renderComponent({ hasComment: false });
      const nodeTitle = screen.getByText('Test Company').closest('.node-title');

      // Check that no spacing element exists
      const spacingElement = nodeTitle.querySelector('span[style*="margin-left"]');
      expect(spacingElement).toBeNull();
    });

    it('should render comments icon wrapper within node-title span', () => {
      renderComponent({ hasComment: true });
      const nodeTitle = screen.getByText('Test Company').closest('.node-title');
      const commentsIconWrapper = screen.getByTestId('comments-icon');

      expect(nodeTitle.contains(commentsIconWrapper)).toBe(true);
    });
  });

  describe('Comments Icon with Descendant Count', () => {
    it('should render comments icon after descendant count', () => {
      // Mock countChildren to return a value
      const { countChildren } = require('../../../app/helpers/ProjectTree');
      countChildren.mockReturnValue(5);

      renderComponent({
        hasComment: true,
        childrenStatusCounts: { ok: 3, incomplete: 2 },
        children: [<div key="child">Child</div>], // Add children to trigger descendant count rendering
      });

      const nodeTitle = screen.getByText('Test Company').closest('.node-title');
      const commentsIcon = screen.getByTestId('comments-icon');

      // Check that comments icon is rendered within the node title
      expect(nodeTitle.contains(commentsIcon)).toBe(true);

      // Check that the descendant count logic is working (even if not visible in this test setup)
      expect(countChildren).toHaveBeenCalledWith({ ok: 3, incomplete: 2 });
    });
  });

  describe('Comments Icon Accessibility', () => {
    it('should have proper accessibility attributes', () => {
      renderComponent({ hasComment: true, hasUnreadComment: true });
      const commentIcon = screen.getByTestId('comment-icon');

      expect(commentIcon).toHaveAttribute('aria-label');
      expect(commentIcon.tagName.toLowerCase()).toBe('i');
    });

    it('should be keyboard accessible', () => {
      renderComponent({ hasComment: true });
      const commentsIcon = screen.getByTestId('comments-icon');

      // Check that it's focusable (though i elements aren't typically focusable by default)
      // In a real implementation, this might be wrapped in a button or have tabIndex
      expect(commentsIcon).toBeInTheDocument();
    });
  });

  describe('Comments Icon Integration with Other Features', () => {
    it('should render comments icon alongside status icon', () => {
      renderComponent({ hasComment: true, hasStatusIcon: true });
      expect(screen.getByTestId('status-icon')).toBeInTheDocument();
      expect(screen.getByTestId('comments-icon')).toBeInTheDocument();
    });

    it('should render comments icon with menu when both are enabled', () => {
      renderComponent({ hasComment: true, hasEditAction: true });
      expect(screen.getByTestId('comments-icon')).toBeInTheDocument();
      expect(screen.getByTestId('node-menu')).toBeInTheDocument();
    });

    it('should handle comments icon with long company names', () => {
      const longName = 'Very Long Company Name That Might Wrap Or Cause Layout Issues';
      renderComponent({ hasComment: true, name: longName });

      const nodeTitle = screen.getByText(longName).closest('.node-title');
      const commentsIconWrapper = screen.getByTestId('comments-icon');

      expect(nodeTitle.contains(commentsIconWrapper)).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined hasComment prop gracefully', () => {
      const propsWithoutHasComment = { ...mockProps };
      delete propsWithoutHasComment.hasComment;

      renderComponent(propsWithoutHasComment);
      expect(screen.queryByTestId('comments-icon')).not.toBeInTheDocument();
    });

    it('should handle undefined hasUnreadComment prop gracefully', () => {
      renderComponent({
        hasComment: true,
        hasUnreadComment: undefined,
      });

      const commentsIcon = screen.getByTestId('comments-icon');
      expect(commentsIcon).not.toHaveClass('text-primary');
    });

    it('should handle null companyId gracefully', () => {
      renderComponent({ hasComment: true, companyId: null });
      const commentsIconWrapper = screen.getByTestId('comments-icon');
      fireEvent.click(commentsIconWrapper);

      expect(companyDetailsTreeMenuClicked).toHaveBeenCalledWith(
        expect.any(Object),
        null,
        'Test Company',
        'supplier-456'
      );
    });
  });
  describe('Write comment menu item', () => {
    it('should render write comment menu item when hasCommentAction is true', () => {
      renderComponent({ hasCommentAction: true });

      const writeCommentItem = screen.getByTestId('dropdown-write-comment');
      expect(writeCommentItem).toBeInTheDocument();
      expect(writeCommentItem).toHaveTextContent('Write comment');
    });

    it('should not render write comment menu item when hasCommentAction is false', () => {
      renderComponent({ hasCommentAction: false });

      const writeCommentItem = screen.queryByTestId('dropdown-write-comment');
      expect(writeCommentItem).not.toBeInTheDocument();
    });

    it('should call details callback when write comment menu item is clicked', () => {
      const companyDetailsTreeMenuClicked =
        require('../../../app/actions/actionCreators/CompanyDetailsTreeMenuClicked');

      renderComponent({
        hasCommentAction: true,
      });

      const writeCommentItem = screen.getByTestId('dropdown-write-comment');
      fireEvent.click(writeCommentItem);

      expect(companyDetailsTreeMenuClicked).toHaveBeenCalledTimes(1);
    });
  });

});
