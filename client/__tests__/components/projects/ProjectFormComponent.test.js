/* eslint-env jest */
// eslint-disable max-len
import React from 'react';
import { IntlProvider } from 'react-intl';
import { MemoryRouter } from 'react-router-dom';
import { render, fireEvent } from '@testing-library/react';
import en from '../../../app/localizations/en.json';
import ProjectFormComponent, { mapStoreToState } from '../../../app/components/projects/ProjectFormComponent';
import { projectViewStore, projectSaveStore, authStore } from '../../../app/stores/Stores';
import saveProjectData from '../../../app/actions/actionCreators/SaveProjectData';
import { featureActive } from '../../../app/helpers/FeatureFlags';

jest.mock('../../../app/helpers/FeatureFlags');

// Helper function to mock feature toggles
featureActive.mockImplementation((feature) => {
  const features = {
    pre_announcements: true,
    add_project_client: true,
    block_project_client: true,
  };
  return features[feature];
});

jest.mock('../../../app/actions/actionCreators/SaveProjectData', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('../../../app/stores/Stores', () => ({
  authStore: {
    getState: jest.fn(() => ({
      auth_info: {
        topMenuParameters: {
          allRepresentations: ['active-org-id'],
          selectedRepresentationIndex: 0,
        },
      },
    })),
    addChangeListener: jest.fn(),
    addListener: jest.fn(),
    removeChangeListener: jest.fn(),
  },
  projectViewStore: {
    getState: jest.fn(() => ({
      added_client_company_external_id: 'added-client-company-external-id',
      end_date: '2024-09-09',
    })),
    addChangeListener: jest.fn(),
    addListener: jest.fn(),
    removeChangeListener: jest.fn(),
  },
  projectSaveStore: {
    getState: jest.fn(() => ({ errors: [] })),
    addChangeListener: jest.fn(),
    addListener: jest.fn(),
    removeChangeListener: jest.fn(),
  },
}));

let mockActivate;
let mockDeactivate;

jest.mock('../../../app/helpers/StoreSubscription', () =>
  jest.fn().mockImplementation(() => {
    mockActivate = jest.fn();
    mockDeactivate = jest.fn();
    return {
      activate: mockActivate,
      deactivate: mockDeactivate,
    };
  })
);

describe('ProjectFormComponent', () => {
  const renderWithReactIntl = component =>
    render(
      <IntlProvider locale='en' messages={en}>
        <MemoryRouter>
          {component}
        </MemoryRouter>
      </IntlProvider>
    );
  // Internal Project ID (chosen freely) or Construction site ID (provided by the tax office) 
  const setup = () => {
    const utils = renderWithReactIntl(<ProjectFormComponent params={{ itemId: '123' }} />);
    const form = utils.container.querySelector('form');
    const projectNameField = utils.getByLabelText(/Project name/i);
    const taxIdField = utils.getByLabelText('Construction site ID');
    const internalIdField = utils.getByLabelText(/Internal Project ID/i);
    const noTaxIdField = utils.getByLabelText('This project does not require a Construction Site ID');
    const stateField = utils.getByLabelText(/State/i);
    const startDateField = utils.getByLabelText(/Start date/i);
    const endDateField = utils.getByLabelText(/End date/i);
  
    return {
      form,
      projectNameField,
      taxIdField,
      internalIdField,
      noTaxIdField,
      stateField,
      startDateField,
      endDateField,
      ...utils,
    };
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should render without crashing', () => {
    expect(renderWithReactIntl(<ProjectFormComponent />)).toBeTruthy();
  });

  test('should map store state correctly', () => {
    const expectedState = {
      add_subcontractor_company_name: undefined,
      add_subcontractor_is_open: undefined,
      add_subcontractor_supplier_id: undefined,
      add_subcontractor_supplier_type: undefined,
      addedClientCanView: undefined,
      addedClientCanViewInitial: undefined,
      addedClientCompanyId: undefined,
      addedClientCompanyExternalId: 'added-client-company-external-id',
      addedClientContactPersonEmail: undefined,
      addedClientContactPersonFullName: undefined,
      addedClientContactPersonId: undefined,
      addedClientName: undefined,
      auto_end_date: undefined,
      createdByOrgName: 'active-org-id',
      end_date: '2024-09-09',
      errors: [],
      failedToLoad: undefined,
      internalProjectId: undefined,
      loaded: undefined,
      loading: undefined,
      name: undefined,
      pa_form_enabled: undefined,
      permissions: undefined,
      projectClientEditIsOpen: undefined,
      projectCreatorRole: undefined,
      saveInProgress: undefined,
      selectedProjectId: undefined,
      start_date: undefined,
      state: undefined,
      subcontractor_edit_supplier_data: undefined,
      tax_id: undefined,
    }
    const mappedState = mapStoreToState(projectViewStore.getState(), projectSaveStore.getState(), authStore.getState(), true)
    expect(mappedState).toMatchObject(expectedState);
  });

  test('should call saveProjectData with the correct data', () => {
    const { form, projectNameField, taxIdField, internalIdField, noTaxIdField, stateField, startDateField, endDateField } = setup();

    fireEvent.change(projectNameField, { target: { value: 'Test Project' } });
    fireEvent.change(taxIdField, { target: { value: '*********' } });
    fireEvent.change(internalIdField, { target: { value: 'internal-001' } });
    fireEvent.click(noTaxIdField);
    fireEvent.change(stateField, { target: { value: 'draft' } });
    fireEvent.change(startDateField, { target: { value: '2024-01-01' } });

    fireEvent.submit(form);

    expect(saveProjectData).toHaveBeenCalledWith(
      {
        name: 'Test Project',
        tax_id: '*********',
        project_id: 'internal-001',
        no_tax_id: true,
        state: 'draft',
        start_date: '2024-01-01',
        end_date: '2024-09-09',
        pa_form_enabled: undefined,
        client_contact_person_email: undefined,
        client_contact_person_id: undefined,
        client_company_id: undefined,
        project_creator_role: undefined,
        added_client_can_view: false,
        client_company_external_id: 'added-client-company-external-id'
      },
      undefined,
      expect.any(Function)
    );
  });
});
// eslint-enable max-len
