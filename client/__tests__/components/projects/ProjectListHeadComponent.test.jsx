import React from 'react';
import { IntlProvider } from 'react-intl';
import { render } from '@testing-library/react';
import ProjectListHeadComponent
  from "../../../app/components/projects/ProjectListHeadComponent";
import en from '../../../app/localizations/en.json';
import sv from '../../../app/localizations/sv.json';

const renderWithReactIntl = (component, locale = 'sv') =>
    render(
      <IntlProvider locale={locale} messages={locale === 'sv' ? sv : en}>
        {component}
      </IntlProvider>
    );

describe('ProjectListHeadComponentTest', () => {
  const testParams = [
    ['sv', 'Kommentarer i projekt'],
    ['en', 'Comments in project'],
  ]
  test.each(testParams)('should not render the comments header without hideComments param', (language, expected) => {
    const {container} = renderWithReactIntl(
      <ProjectListHeadComponent/>, language,
    )
    expect(container.textContent).not.toContain(expected);
  });

  test.each(testParams)('should render the comments header when hideComments param false', (language, expected) => {
    const {container} = renderWithReactIntl(
      <ProjectListHeadComponent hideComments={false}/>, language,
    )
    expect(container.textContent).toContain(expected);
  });

  test.each(testParams)('should not render the comments header when hideComments param true', (language, expected) => {
    const {container} = renderWithReactIntl(
      <ProjectListHeadComponent hideComments={true}/>, language,
    )
    expect(container.textContent).not.toContain(expected);
  });
});
