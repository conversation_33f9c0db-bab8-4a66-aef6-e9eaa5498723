import React from 'react';
import { IntlProvider } from 'react-intl';
import { render } from '@testing-library/react';
import ProjectListRowComponent
  from "../../../app/components/projects/ProjectListRowComponent";
import en from '../../../app/localizations/en.json';
import sv from '../../../app/localizations/sv.json';

const renderWithReactIntl = (component, locale = 'sv') =>
    render(
      <IntlProvider locale={locale} messages={locale === 'sv' ? sv : en}>
        {component}
      </IntlProvider>
    );

describe('ProjectListRowComponentTest', () => {
  const project = {
    id: 'project-id',
    name: 'test',
    project_status: 'stop',
    project_state: 'draft',
  }
  test.each([
    [true, false, true],
    [false, true, false],
    [false, false, false], // Default case without params
    [true, true, false],
    ])('test whether should display comments icon', (hasComments, hideComments, showIcon) => {
      const {container} = renderWithReactIntl(
      <ProjectListRowComponent project={project} hideComments={hideComments} hasComments={hasComments}/>
    )
    if (showIcon)
      expect(container.querySelector('.fa-comments-o')).toBeInTheDocument();
    else
      expect(container.querySelector('.fa-comments-o')).not.toBeInTheDocument();
  });
});
