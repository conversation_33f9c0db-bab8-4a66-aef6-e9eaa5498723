/* global jest, describe, it, expect, beforeEach */
import React from 'react';
import { IntlProvider } from 'react-intl';
import { render, screen, waitFor } from '@testing-library/react';
import {
  authStore,
  cardsStore,
  companyViewStore,
  paFormStore,
  projectViewStore,
} from '../../../app/stores/Stores';
// Import actual store classes for getInitialState
import AuthStore from '../../../app/stores/AuthStore';
import CardsStore from '../../../app/stores/CardsStore';
import CompanyViewStore from '../../../app/stores/CompanyViewStore';
import PAFormStore from '../../../app/stores/PAFormStore';
import ProjectViewStore from '../../../app/stores/ProjectViewStore';

const mockDispatcher = {
  register: jest.fn(),
};

const authStoreInitialState = new AuthStore(mockDispatcher).getInitialState();
const cardsStoreInitialState = new CardsStore(mockDispatcher).getInitialState();
const companyViewStoreInitialState = new CompanyViewStore(mockDispatcher).getInitialState();
const paFormStoreInitialState = new PAFormStore(mockDispatcher).getInitialState();
const projectViewStoreInitialState = new ProjectViewStore(mockDispatcher).getInitialState();

import companyProjectCommentsClear 
  from '../../../app/actions/actionCreators/CompanyProjectCommentsClear';
import companyProjectCommentsSelect 
  from '../../../app/actions/actionCreators/CompanyProjectCommentsSelect';
import en from '../../../app/localizations/en.json';
import sv from '../../../app/localizations/sv.json';
import PreannouncementForm from '../../../app/components/projects/PreannouncementForm';

// Mock feature flags
jest.mock('../../../app/helpers/FeatureFlags', () => ({
  featureActive: jest.fn(flag => {
    if (flag === 'project_supplier_comments') return true;
    return false;
  }),
}));

// Mock action creators
jest.mock('../../../app/actions/actionCreators/CompanyProjectCommentsClear', () => jest.fn());
jest.mock('../../../app/actions/actionCreators/CompanyProjectCommentsSelect', () => jest.fn());

const createMockStore = () => ({
  getState: jest.fn(),
  addChangeListener: jest.fn(),
  addListener: jest.fn(),
  removeChangeListener: jest.fn(),
});

// Mock stores
jest.mock('../../../app/stores/Stores', () => ({
  authStore: createMockStore(),
  cardsStore: createMockStore(),
  companyViewStore: createMockStore(),
  paFormStore: createMockStore(),
  projectViewStore: createMockStore(),
}));

// Mock StoreSubscription
const mockActivate = jest.fn();
const mockDeactivate = jest.fn();

jest.mock('../../../app/helpers/StoreSubscription', () =>
  jest.fn().mockImplementation(() => ({
    activate: mockActivate,
    deactivate: mockDeactivate,
  }))
);

jest.mock('../../../app/components/companies/ProjectCommentsListComponent', () => {
  return () => (<span>mocked comment list</span>);
});
jest.mock('../../../app/components/companies/ProjectCommentsAddCommentComponent', () => {
  return () => (<span>mocked comment add component</span>);
});

describe('PreannouncementView - Comments Feature (BOL-6252)', () => {
  // Test constants for comments feature
  const TEST_IDS = {
    ORG: 'test-org-123',
    COMPANY: 'test-company-123',
  };

  // State factories for comments feature testing
  const createAuthState = (overrides = {}) => ({
    ...authStoreInitialState,
    profile: {
      organisation_id: TEST_IDS.ORG,
    },
    ...overrides,
  });

  const createCompanyState = (overrides = {}) => ({
    ...companyViewStoreInitialState,
    company_loaded: true,
    gov_org_ids: [],
    ...overrides,
  });

  const createPaFormState = (overrides = {}) => ({
    ...paFormStoreInitialState,
    current_pa_status: 'registered',
    current_pa_company_details: {
      company_id: TEST_IDS.COMPANY,
      name: 'Test Company',
    },
    ...overrides,
  });

  const createProjectState = (overrides = {}) => ({
    ...projectViewStoreInitialState,
    ...overrides,
  });

  const createCardState = (overrides = {}) => ({
    ...cardsStoreInitialState,
    ...overrides,
  });

  const setupStores = (overrides = {}) => {
    authStore.getState.mockReturnValue(createAuthState(overrides.auth));
    companyViewStore.getState.mockReturnValue(createCompanyState(overrides.company));
    paFormStore.getState.mockReturnValue(createPaFormState(overrides.paForm));
    projectViewStore.getState.mockReturnValue(createProjectState(overrides.project));
    cardsStore.getState.mockReturnValue(createCardState(overrides.cards));
  };

  const renderPreannouncementForm = (storeOverrides = {}, locale = 'sv') => {
    setupStores(storeOverrides);
    return render(
      <IntlProvider locale={locale} messages={locale === 'sv' ? sv : en}>
        <PreannouncementForm />
      </IntlProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Permission checks for comments section', () => {
    it('should render form with comments when user has permission', async () => {
      expect(companyProjectCommentsClear).not.toHaveBeenCalled();
      renderPreannouncementForm({
        paForm: { pa_supplier_permissions: ['view_supplier_comments'], for_supplier_id: '1' },
      });

      expect(screen.queryByText('Kommentarer')).toBeInTheDocument();
      await waitFor(() => expect(companyProjectCommentsSelect).toHaveBeenCalled());
      expect(companyProjectCommentsClear).not.toHaveBeenCalled();
    });

    it('should render form without comments when user lacks permission', async () => {
      renderPreannouncementForm({
        paForm: { pa_supplier_permissions: [], for_supplier_id: '1' },
      });

      expect(screen.queryByText('Kommentarer')).not.toBeInTheDocument();
      await waitFor(() => expect(companyProjectCommentsClear).toHaveBeenCalled());
      expect(companyProjectCommentsSelect).not.toHaveBeenCalled();
    });
  });
});
