/* global jest, describe, it, expect, beforeEach */
import React from 'react';
import { IntlProvider } from 'react-intl';
import { render, screen, fireEvent } from '@testing-library/react';
import PreannouncementView from '../../../app/components/projects/PreannouncementView';
import {
  authStore,
  companyViewStore,
  commentsStore,
  paFormStore,
  projectViewStore,
  paReviewStore,
} from '../../../app/stores/Stores';
// Import actual store classes for getInitialState
import AuthStore from '../../../app/stores/AuthStore';
import CompanyViewStore from '../../../app/stores/CompanyViewStore';
import CommentsStore from '../../../app/stores/CommentsStore';
import PAFormStore from '../../../app/stores/PAFormStore';
import ProjectViewStore from '../../../app/stores/ProjectViewStore';
import PAreviewStore from '../../../app/stores/PAreviewStore';

const mockDispatcher = {
  register: jest.fn(),
};

const authStoreInitialState = new AuthStore(mockDispatcher).getInitialState();
const companyViewStoreInitialState = new CompanyViewStore(mockDispatcher).getInitialState();
const commentsStoreInitialState = new CommentsStore(mockDispatcher).getInitialState();
const paFormStoreInitialState = new PAFormStore(mockDispatcher).getInitialState();
const projectViewStoreInitialState = new ProjectViewStore(mockDispatcher).getInitialState();
const paReviewStoreInitialState = new PAreviewStore(mockDispatcher).getInitialState();

import confirmPreannouncement from '../../../app/actions/actionCreators/ConfirmPreannouncement';
import rejectPreannouncement from '../../../app/actions/actionCreators/RejectPreannouncement';
import updatePAreviewFail from '../../../app/actions/actionCreators/UpdatePAreviewFail';
import en from '../../../app/localizations/en.json';
import sv from '../../../app/localizations/sv.json';

// Mock feature flags
jest.mock('../../../app/helpers/FeatureFlags', () => ({
  featureActive: jest.fn(flag => {
    if (flag === 'project_supplier_comments') return true;
    return false;
  }),
}));

// Mock action creators
jest.mock('../../../app/actions/actionCreators/ConfirmPreannouncement', () => jest.fn());
jest.mock('../../../app/actions/actionCreators/RejectPreannouncement', () => jest.fn());
jest.mock('../../../app/actions/actionCreators/UpdatePAreviewFail', () => jest.fn());

const createMockStore = () => ({
  getState: jest.fn(),
  addChangeListener: jest.fn(),
  addListener: jest.fn(),
  removeChangeListener: jest.fn(),
});

// Mock stores
jest.mock('../../../app/stores/Stores', () => ({
  authStore: createMockStore(),
  companyViewStore: createMockStore(),
  commentsStore: createMockStore(),
  paFormStore: createMockStore(),
  projectViewStore: createMockStore(),
  paReviewStore: createMockStore(),
}));

// Mock router
const mockNavigate = jest.fn();
jest.mock('../../../app/helpers/RouterComponentWrappers', () => ({
  withRouter: Component => props => (
    <Component {...props} router={{ navigate: mockNavigate, location: {} }} />
  ),
}));

// Mock StoreSubscription
const mockActivate = jest.fn();
const mockDeactivate = jest.fn();

jest.mock('../../../app/helpers/StoreSubscription', () =>
  jest.fn().mockImplementation(() => ({
    activate: mockActivate,
    deactivate: mockDeactivate,
  }))
);

// Mock CompanyDetailsComponent
jest.mock('../../../app/components/companies/CompanyDetailsComponent', () => {
  function MockCompanyDetails() {
    return <span>mocked details</span>;
  }
  return MockCompanyDetails;
});

// Mock Preannouncementform component
jest.mock('../../../app/components/projects/PreannouncementForm', () => {
  function MockPreannouncementForm() {
    return <span>mocked form</span>;
  }
  return MockPreannouncementForm;
});

describe('PreannouncementView - Comments Feature (BOL-6252)', () => {
  // Test constants for comments feature
  const TEST_IDS = {
    ORG: 'test-org-123',
    COMPANY: 'test-company-123',
    PA: 'test-pa-123',
  };

  const TRANSLATION_KEYS = {
    COMMENT_ERROR: 'preannouncementDetails.comment_in_progress_error',
    COMMENTS_HEADER: 'preannouncementDetails.comments_header',
  };

  // State factories for comments feature testing
  const createAuthState = (overrides = {}) => ({
    ...authStoreInitialState,
    profile: {
      organisation_id: TEST_IDS.ORG,
      email: '<EMAIL>',
    },
    ...overrides,
  });

  const createCompanyState = (overrides = {}) => ({
    ...companyViewStoreInitialState,
    company_loaded: true,
    gov_org_ids: [],
    ...overrides,
  });

  const createCommentsState = (overrides = {}) => ({
    ...commentsStoreInitialState,
    ...overrides,
  });

  const createPaFormState = (overrides = {}) => ({
    ...paFormStoreInitialState,
    pa_view_is_open: true,
    current_pa_status: 'registered',
    current_pa_company_details: {
      company_id: TEST_IDS.COMPANY,
      name: 'Test Company',
    },
    current_pa_id: TEST_IDS.PA,
    assigned_to_company_id: TEST_IDS.ORG,
    created_by_supplier_org: {
      id: 'test-supplier-org-123',
    },
    pa_supplier_permissions: ['view_supplier_comments'],
    for_supplier_id: '1',
    ...overrides,
  });

  const createProjectState = (overrides = {}) => ({
    ...projectViewStoreInitialState,
    selected_project_id: 'test-project-123',
    ...overrides,
  });

  const createPaReviewState = (overrides = {}) => ({
    ...paReviewStoreInitialState,
    ...overrides,
  });

  const setupStores = (overrides = {}) => {
    authStore.getState.mockReturnValue(createAuthState(overrides.auth));
    companyViewStore.getState.mockReturnValue(createCompanyState(overrides.company));
    commentsStore.getState.mockReturnValue(createCommentsState(overrides.comments));
    paFormStore.getState.mockReturnValue(createPaFormState(overrides.paForm));
    projectViewStore.getState.mockReturnValue(createProjectState(overrides.project));
    paReviewStore.getState.mockReturnValue(createPaReviewState(overrides.paReview));
  };

  const renderPreannouncementView = (storeOverrides = {}, locale = 'sv') => {
    setupStores(storeOverrides);
    return render(
      <IntlProvider locale={locale} messages={locale === 'sv' ? sv : en}>
        <PreannouncementView />
      </IntlProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Comments functionality - BOL-6252', () => {
    describe('Unsaved comments blocking Confirm/Reject actions', () => {
      it('should prevent confirm when adding comment and show error message', () => {
        renderPreannouncementView({
          comments: { has_unsaved_comment_draft: true },
        });

        fireEvent.click(screen.getByText('Bekräfta'));

        expect(updatePAreviewFail).toHaveBeenCalledWith(null, TRANSLATION_KEYS.COMMENT_ERROR);
        expect(confirmPreannouncement).not.toHaveBeenCalled();
      });

      it('should prevent reject when adding comment and show error message', () => {
        renderPreannouncementView({
          comments: { has_unsaved_comment_draft: true },
        });

        fireEvent.click(screen.getByText('Avvisa'));

        expect(updatePAreviewFail).toHaveBeenCalledWith(null, TRANSLATION_KEYS.COMMENT_ERROR);
        expect(rejectPreannouncement).not.toHaveBeenCalled();
      });

      it('should prevent confirm when editing comment and show error message', () => {
        renderPreannouncementView({
          comments: {
            comments: [{ data: { id: 'comment-1' }, has_unsaved_edits: true }],
          },
        });

        fireEvent.click(screen.getByText('Bekräfta'));

        expect(updatePAreviewFail).toHaveBeenCalledWith(null, TRANSLATION_KEYS.COMMENT_ERROR);
        expect(confirmPreannouncement).not.toHaveBeenCalled();
      });
    });

    describe('Saved comments allowing actions', () => {
      it('should allow confirm when no comments are being edited', () => {
        renderPreannouncementView({
          comments: {
            has_unsaved_comment_draft: false,
            comments: [{ data: { id: 'comment-1' }, has_unsaved_edits: false }],
          },
        });

        fireEvent.click(screen.getByText('Bekräfta'));

        expect(updatePAreviewFail).not.toHaveBeenCalled();
        expect(confirmPreannouncement).toHaveBeenCalledWith(TEST_IDS.PA, 'confirmed');
      });

      it('should allow reject when no comments are being edited', () => {
        renderPreannouncementView({
          comments: {
            has_unsaved_comment_draft: false,
            comments: [{ data: { id: 'comment-1' }, has_unsaved_edits: false }],
          },
        });

        fireEvent.click(screen.getByText('Avvisa'));

        expect(updatePAreviewFail).not.toHaveBeenCalled();
        expect(rejectPreannouncement).toHaveBeenCalledWith(TEST_IDS.PA, 'rejected');
      });
    });

    describe('Error message display', () => {
      it('should display Swedish error message for unsaved comments', () => {
        renderPreannouncementView({
          paReview: { error_message_id: TRANSLATION_KEYS.COMMENT_ERROR },
        });

        const errorText = [
          'Det finns kommentarer med ändringar som inte har sparats.',
          'För att kunna fortsätta måste du spara eller avbryta dessa ändringar.',
        ].join(' ');
        expect(screen.getByText(errorText)).toBeInTheDocument();
      });

      it('should display English error message for unsaved comments', () => {
        renderPreannouncementView(
          {
            paReview: { error_message_id: TRANSLATION_KEYS.COMMENT_ERROR },
          },
          'en'
        );

        const errorText = [
          'There are comments with changes that have not been saved.',
          'To continue, please save or cancel these changes.',
        ].join(' ');
        expect(screen.getByText(errorText)).toBeInTheDocument();
      });
    });
  });
});
