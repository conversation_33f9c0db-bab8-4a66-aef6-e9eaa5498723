/* eslint-env jest */
/* global jest, describe, it, expect, beforeEach */

import React from 'react';
import { IntlProvider } from 'react-intl';
import { render, screen } from '@testing-library/react';
import CommentIconComponent from '../../../app/components/shared/CommentIconComponent';
import en from '../../../app/localizations/en.json';

// Mock react-intl's injectIntl to avoid complexity in testing
jest.mock('react-intl', () => ({
  ...jest.requireActual('react-intl'),
  injectIntl: (Component) => (props) => <Component {...props} intl={{
    formatMessage: (message) => message.defaultMessage || message.id,
    locale: 'en',
  }} />,
}));

const renderWithIntl = (component) =>
  render(
    <IntlProvider locale="en" messages={en}>
      {component}
    </IntlProvider>
  );

describe('CommentIconComponent', () => {
  describe('Basic Rendering', () => {
    it('should render without crashing', () => {
      const { container } = renderWithIntl(<CommentIconComponent />);
      expect(container).toBeInTheDocument();
    });

    it('should not render anything when has_comment is false', () => {
      const { container } = renderWithIntl(<CommentIconComponent has_comment={false} />);
      expect(container.firstChild).toBeNull();
    });

    it('should not render anything when has_comment is not provided (defaults to false)', () => {
      const { container } = renderWithIntl(<CommentIconComponent />);
      expect(container.firstChild).toBeNull();
    });

    it('should render an icon element when has_comment is true', () => {
      renderWithIntl(<CommentIconComponent has_comment={true} />);
      const icon = screen.getByTestId('comment-icon');
      expect(icon).toBeInTheDocument();
      expect(icon.tagName.toLowerCase()).toBe('i');
    });
  });

  describe('Icon Types', () => {
    it('should render fa-comments-o for read comments (has_unread_comment is false)', () => {
      renderWithIntl(<CommentIconComponent has_comment={true} has_unread_comment={false} />);
      const icon = screen.getByTestId('comment-icon');
      expect(icon).toHaveClass('fa-comments-o');
      expect(icon).not.toHaveClass('fa-comments');
    });

    it('should render fa-comments for unread comments (has_unread_comment is true)', () => {
      renderWithIntl(<CommentIconComponent has_comment={true} has_unread_comment={true} />);
      const icon = screen.getByTestId('comment-icon');
      expect(icon).toHaveClass('fa-comments');
      expect(icon).not.toHaveClass('fa-comments-o');
    });

    it('should default to read state when has_unread_comment is not provided', () => {
      renderWithIntl(<CommentIconComponent has_comment={true} />);
      const icon = screen.getByTestId('comment-icon');
      expect(icon).toHaveClass('fa-comments-o');
      expect(icon).not.toHaveClass('fa-comments');
    });
  });

  describe('Styling', () => {
    it('should apply gray color for read comments', () => {
      renderWithIntl(<CommentIconComponent has_comment={true} has_unread_comment={false} />);
      const icon = screen.getByTestId('comment-icon');
      expect(icon).toHaveStyle({ color: '#999999' });
    });

    it('should apply blue color for unread comments', () => {
      renderWithIntl(<CommentIconComponent has_comment={true} has_unread_comment={true} />);
      const icon = screen.getByTestId('comment-icon');
      expect(icon).toHaveStyle({ color: '#32A1D1' });
    });

    it('should always have fa class', () => {
      renderWithIntl(<CommentIconComponent has_comment={true} />);
      const icon = screen.getByTestId('comment-icon');
      expect(icon).toHaveClass('fa');
    });
  });

  describe('Accessibility', () => {
    it('should have correct ARIA label for read comments', () => {
      renderWithIntl(<CommentIconComponent has_comment={true} has_unread_comment={false} />);
      const icon = screen.getByTestId('comment-icon');
      expect(icon).toHaveAttribute('aria-label', 'Read comments');
    });

    it('should have correct ARIA label for unread comments', () => {
      renderWithIntl(<CommentIconComponent has_comment={true} has_unread_comment={true} />);
      const icon = screen.getByTestId('comment-icon');
      expect(icon).toHaveAttribute('aria-label', 'Unread comments');
    });

    it('should have aria-label attribute', () => {
      renderWithIntl(<CommentIconComponent has_comment={true} />);
      const icon = screen.getByTestId('comment-icon');
      expect(icon).toHaveAttribute('aria-label');
    });
  });

  describe('Internationalization', () => {
    it('should render with proper ARIA labels using IntlProvider', () => {
      // Test that the component renders correctly with IntlProvider
      // The injectIntl HOC should provide the intl prop automatically
      renderWithIntl(<CommentIconComponent has_comment={true} />);

      const icon = screen.getByTestId('comment-icon');
      expect(icon).toHaveAttribute('aria-label', 'Read comments');
    });

    it('should work with default locale setup', () => {
      // Test that the component works correctly with the default test setup
      renderWithIntl(<CommentIconComponent has_comment={true} />);

      const icon = screen.getByTestId('comment-icon');
      expect(icon).toHaveAttribute('aria-label');
      expect(typeof icon.getAttribute('aria-label')).toBe('string');
    });
  });

  describe('Prop Validation', () => {
    it('should handle boolean values correctly', () => {
      // Test various boolean values
      const testCases = [
        { has_comment: true, has_unread_comment: true },
        { has_comment: true, has_unread_comment: false },
        { has_comment: false, has_unread_comment: true },
        { has_comment: false, has_unread_comment: false },
      ];

      testCases.forEach(({ has_comment, has_unread_comment }) => {
        const { container } = renderWithIntl(
          <CommentIconComponent has_comment={has_comment} has_unread_comment={has_unread_comment} />
        );

        if (has_comment) {
          expect(container.firstChild).not.toBeNull();
        } else {
          expect(container.firstChild).toBeNull();
        }
      });
    });

    it('should handle undefined props gracefully', () => {
      // Test with undefined props (should use defaults)
      const { container } = renderWithIntl(<CommentIconComponent />);
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Edge Cases', () => {
    it('should handle null values for has_comment', () => {
      const { container } = renderWithIntl(<CommentIconComponent has_comment={null} />);
      expect(container.firstChild).toBeNull();
    });

    it('should handle null values for has_unread_comment', () => {
      renderWithIntl(<CommentIconComponent has_comment={true} has_unread_comment={null} />);
      const icon = screen.getByTestId('comment-icon');
      // Should default to read state (gray color, fa-comments-o)
      expect(icon).toHaveStyle({ color: '#999999' });
      expect(icon).toHaveClass('fa-comments-o');
    });

    it('should handle non-boolean values by treating them as falsy', () => {
      // Test with non-boolean values that should be treated as falsy
      // Note: In practice, PropTypes will warn about this, but the component should still handle it gracefully
      const { container } = renderWithIntl(<CommentIconComponent has_comment={undefined} />);
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Component Structure', () => {
    it('should render as a single icon element', () => {
      renderWithIntl(<CommentIconComponent has_comment={true} />);
      const icon = screen.getByTestId('comment-icon');

      // Should be the only child of the component
      expect(icon.parentElement.children).toHaveLength(1);
      expect(icon).toBe(icon.parentElement.firstChild);
    });

    it('should not have any wrapper elements', () => {
      const { container } = renderWithIntl(<CommentIconComponent has_comment={true} />);

      // The container should only contain the icon element directly
      expect(container.children).toHaveLength(1);
      expect(container.firstChild.tagName.toLowerCase()).toBe('i');
    });
  });
});