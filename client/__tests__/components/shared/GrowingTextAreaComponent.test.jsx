/* global jest, describe, it, expect, afterEach */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import GrowingTextAreaComponent from '../../../app/components/shared/GrowingTextAreaComponent';

describe('GrowingTextAreaComponent', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders with default props', () => {
    render(<GrowingTextAreaComponent />);
    const textarea = screen.getByRole('textbox');
    expect(textarea).toBeInTheDocument();
    expect(textarea).toHaveClass('form-control');
    expect(textarea).toHaveAttribute('rows', '1');
  });

  it('changes rows based on the isExpanded property', () => {
    const {rerender} = render(<GrowingTextAreaComponent isExpanded={false}/>);
    const textarea = screen.getByRole('textbox');
    expect(textarea).toHaveAttribute('rows', '1');
    rerender(<GrowingTextAreaComponent isExpanded={true}/>);
    expect(textarea).toHaveAttribute('rows', '4');
  });

  it('calls onInput, onFocus, and onBlur callbacks', () => {
    const mockOnInput = jest.fn();
    const mockOnFocus = jest.fn();
    const mockOnBlur = jest.fn();

    render(
      <GrowingTextAreaComponent onInput={mockOnInput} onFocus={mockOnFocus} onBlur={mockOnBlur} />
    );

    const textarea = screen.getByRole('textbox');

    // Test focus callback
    fireEvent.focus(textarea);
    expect(mockOnFocus).toHaveBeenCalledTimes(1);

    // Test input callback
    fireEvent.input(textarea, { target: { value: 'Test' } });
    expect(mockOnInput).toHaveBeenCalledTimes(1);

    // Test blur callback
    fireEvent.blur(textarea);
    expect(mockOnBlur).toHaveBeenCalledTimes(1);
  });

  it('forwards ref to the textarea element', () => {
    const ref = React.createRef();
    render(<GrowingTextAreaComponent ref={ref} />);

    const textarea = screen.getByRole('textbox');
    expect(ref.current).toBe(textarea);
  });

  it('passes through additional props to the textarea', () => {
    render(
      <GrowingTextAreaComponent
        placeholder="Test placeholder"
        disabled={true}
        name="test-name"
        id="test-id"
        className="test-class"
      />
    );

    const textarea = screen.getByRole('textbox');
    expect(textarea).toHaveAttribute('placeholder', 'Test placeholder');
    expect(textarea).toBeDisabled();
    expect(textarea).toHaveAttribute('name', 'test-name');
    expect(textarea).toHaveAttribute('id', 'test-id');
    expect(textarea).toHaveClass('test-class form-control');
  });

  it('has 1 row if no value prop is provided', () => {
    render(<GrowingTextAreaComponent />);
    const textarea = screen.getByRole('textbox');
    expect(textarea).toHaveAttribute('rows', '1');
  });

  it('has 4 rows if a value prop is provided', () => {
    render(<GrowingTextAreaComponent value="Test content" />);
    const textarea = screen.getByRole('textbox');
    expect(textarea).toHaveAttribute('rows', '4');
  });
});
