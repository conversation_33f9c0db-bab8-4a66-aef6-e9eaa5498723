import '@testing-library/jest-dom';
import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import TabsComponent from '../../../app/components/shared/TabsComponent';

// Mock the reactstrap components
jest.mock('reactstrap', () => ({
  TabContent: ({ children }) => <div data-testid="tab-content">{children}</div>,
  TabPane: ({ children }) => <div data-testid="tab-pane">{children}</div>,
  Nav: ({ children }) => <nav data-testid="nav">{children}</nav>,
  NavItem: ({ children }) => <div data-testid="nav-item">{children}</div>,
  NavLink: ({ children, onClick, className }) => (
    <a data-testid="nav-link" onClick={onClick} href="#" className={className}>
      {children}
    </a>
  ),
  Row: ({ children }) => <div data-testid="row">{children}</div>,
  Col: ({ children }) => <div data-testid="col">{children}</div>,
}));

const TabChild = ({ title, children, enabled }) => (
  <div title={title} data-enabled={enabled ? 'true' : 'false'}>
    {children}
  </div>
);

describe('TabsComponent', () => {
  it('renders without crashing', () => {
    render(
      <TabsComponent>
        <TabChild title="Tab 1" enabled={true}>
          Content 1
        </TabChild>
        <TabChild title="Tab 2" enabled={true}>
          Content 2
        </TabChild>
      </TabsComponent>
    );
    expect(screen.getByTestId('nav')).toBeInTheDocument();
    expect(screen.getByTestId('tab-content')).toBeInTheDocument();
  });

  it('renders correct number of tabs', () => {
    render(
      <TabsComponent>
        <TabChild title="Tab 1" enabled={true}>
          Content 1
        </TabChild>
        <TabChild title="Tab 2" enabled={true}>
          Content 2
        </TabChild>
        <TabChild title="Tab 3" enabled={false}>
          Content 3
        </TabChild>
      </TabsComponent>
    );
    expect(screen.getAllByTestId('nav-link')).toHaveLength(2);
  });

  it('sets the first tab as active by default', () => {
    render(
      <TabsComponent>
        <TabChild title="Tab 1" enabled={true}>
          Content 1
        </TabChild>
        <TabChild title="Tab 2" enabled={true}>
          Content 2
        </TabChild>
      </TabsComponent>
    );
    const navLinks = screen.getAllByTestId('nav-link');
    expect(navLinks[0]).toHaveClass('active');
    expect(navLinks[1]).not.toHaveClass('active');
  });

  it('changes active tab when clicked', () => {
    render(
      <TabsComponent>
        <TabChild title="Tab 1" enabled={true}>
          Content 1
        </TabChild>
        <TabChild title="Tab 2" enabled={true}>
          Content 2
        </TabChild>
      </TabsComponent>
    );
    const navLinks = screen.getAllByTestId('nav-link');
    fireEvent.click(navLinks[1]);
    expect(navLinks[0]).not.toHaveClass('active');
    expect(navLinks[1]).toHaveClass('active');
  });

  it('renders nothing when no enabled tabs', () => {
    const { container } = render(
      <TabsComponent>
        <TabChild title="Tab 1" enabled={false}>
          Content 1
        </TabChild>
        <TabChild title="Tab 2" enabled={false}>
          Content 2
        </TabChild>
      </TabsComponent>
    );
    expect(container.firstChild).toBeNull();
  });

  it('updates active tab when props change', () => {
    const { rerender } = render(
      <TabsComponent activeTab={0}>
        <TabChild title="Tab 1" enabled={true}>
          Content 1
        </TabChild>
        <TabChild title="Tab 2" enabled={true}>
          Content 2
        </TabChild>
      </TabsComponent>
    );
    
    let navLinks = screen.getAllByTestId('nav-link');
    expect(navLinks[0]).toHaveClass('active');
    
    rerender(
      <TabsComponent activeTab={1}>
        <TabChild title="Tab 1" enabled={true}>
          Content 1
        </TabChild>
        <TabChild title="Tab 2" enabled={true}>
          Content 2
        </TabChild>
      </TabsComponent>
    );
    
    navLinks = screen.getAllByTestId('nav-link');
    expect(navLinks[1]).toHaveClass('active');
  });
});
