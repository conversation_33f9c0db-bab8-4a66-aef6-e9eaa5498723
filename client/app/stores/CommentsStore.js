import BaseStore from './BaseStore';
import ActionTypes from '../actions/ActionTypes';

class CommentsStore extends BaseStore {
  getInitialState() {
    return {
      supplier_id: null,
      comments_loading: false,
      comments_loaded: false,
      comments_failed: false,
      comments: [],
      comment_map: {},
      comment_delete_id: null,
      comment_delete_failed: false,
      comment_add_in_progress: false,
      comment_add_errors: null,
      has_unsaved_comment_draft: false,
    };
  }

  [ActionTypes.DETAILS_VIEW_CLOSE](state) {
    this.resetState(state);
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_CLEAR](state) {
    console.debug('CommentsStore: Clearing comments');
    this.resetState(state);
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_SELECT](state, action) {
    state.comments_loading = true;
    state.comments_loaded = false;
    state.comments_failed = false;
    state.comments = [];
    state.comment_map = {};
    state.supplier_id = action.supplier_id;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_FAILED_TO_LOAD](state /* , action */) {
    state.comments_loading = false;
    state.comments_failed = true;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_LOADED](state, action) {
    state.comments_loaded = true;
    state.comments_loading = false;
    state.comments = action.comments.map(comment => ({
      data: comment,
      loading: false,
      editing: false,
      has_unsaved_edits: false,
      errors: [],
    }));
    state.comment_map = state.comments.reduce((prev, curr) => {
      prev[curr.data.id] = curr;
      return prev;
    }, {});
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_OPEN_DELETE_CONFIRMATION](state, action) {
    state.comment_delete_id = action.comment_delete_id;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_CLOSE_DELETE_CONFIRMATION](state /* , action */) {
    state.comment_delete_id = null;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_TOGGLE_EDIT](state, action) {
    state.comment_map[action.commentId].editing = action.editIsEnabled;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_UPDATE](state, action) {
    state.comment_map[action.commentId].loading = true;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_UPDATE_FAILED](state, action) {
    state.comment_map[action.commentId].loading = false;
    state.comment_map[action.commentId].errors = action.errors;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_UPDATED](state, action) {
    state.comment_map[action.comment.id].loading = false;
    state.comment_map[action.comment.id].data = action.comment;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_DELETE](state, action) {
    state.comment_delete_id = null;
    state.comment_delete_failed = false;
    state.comment_map[action.commentId].loading = true;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_DELETE_FAILED](state, action) {
    state.comment_delete_failed = true;
    state.comment_map[action.commentId].loading = false;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_SET_DRAFT_STATE](state, action) {
    state.has_unsaved_comment_draft = action.hasUnsavedDraft;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_SET_UNSAVED_EDITS](state, action) {
    state.comment_map[action.commentId].has_unsaved_edits = action.hasUnsavedEdits;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_ADD](state, /* action*/) {
    state.comment_add_in_progress = true;
    state.comment_add_errors = null;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_ADD_SUCCESS](state, action) {
    state.comment_add_in_progress = false;
    state.comment_add_errors = null;
    const comment = {
      data: action.comment,
      loading: false,
      editing: false,
      errors: [],
    };
    state.comments.splice(0, 0, comment);
    state.comment_map[comment.data.id] = comment;
  }

  [ActionTypes.COMPANY_PROJECTS_COMMENTS_ADD_FAILED](state, action) {
    state.comment_add_in_progress = false;
    state.comment_add_errors = action.errors;
  }
}

export default CommentsStore;
