import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';
import updateSupplier from './UpdateSupplier';

const companyProjectCommentsSelect = async supplierId => {
  dispatcher.dispatch({
    type: ActionTypes.COMPANY_PROJECTS_COMMENTS_SELECT,
    supplier_id: supplierId,
  });
  try {
    const apiClient = getAPIClient();
    const res = await apiClient.get(`/api/supplier/${supplierId}/comments`, true);
    const data = res.data;

    dispatcher.dispatch({
      type: ActionTypes.COMPANY_PROJECTS_COMMENTS_LOADED,
      comments: data.comments,
    });
    // Update the state of the supplier to match the state of the comments
    updateSupplier(supplierId, {
      has_comment: data.comments.length > 0,
      has_unread_comment: data.comments.some(comment => !comment.is_read),
    });
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_PROJECTS_COMMENTS_FAILED_TO_LOAD,
      error,
    });
  }
};

export default companyProjectCommentsSelect;
