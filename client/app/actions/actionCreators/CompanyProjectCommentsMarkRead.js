import getAPIClient from '../../helpers/ApiClient';

const companyProjectCommentsMarkRead = async (supplierId, commentIds) => {
  if (commentIds.length === 0) {
    return;
  }
  try {
    const apiClient = getAPIClient();
    await apiClient.post(`/api/supplier/${supplierId}/comments/mark-read`, true, {
      comment_ids: commentIds,
    });
  } catch (error) {
    // Something unexpected went wrong. There's no meaningful action to take here.
  }
};

export default companyProjectCommentsMarkRead;
