import deepcopy from 'deepcopy';
import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import { projectViewStore } from '../../stores/Stores';

const openPreannouncementView = (
  navigate,
  projectId,
  preannouncementId,
  status,
  companyDetails,
  buyer,
  supplierId,
  permissions
) => {
  const projectViewState = projectViewStore.getState();
  dispatcher.dispatch({
    type: ActionTypes.PA_VIEW_OPEN,
    projectId,
    paStatus: status,
    paCompany: companyDetails,
    paId: preannouncementId,
    buyer,
    supplierId,
    project_tree: deepcopy(projectViewState.project_tree),
    permissions,
  });
  navigate(`/projects/${projectId}/pa/${preannouncementId}`);
};

export default openPreannouncementView;
