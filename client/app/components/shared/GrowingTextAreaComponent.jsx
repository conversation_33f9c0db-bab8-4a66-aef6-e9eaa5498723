import React, { useState, forwardRef, useEffect } from 'react';
import PropTypes from 'prop-types';

const GrowingTextAreaComponent = forwardRef((props, ref) => {
  // Growing textarea component behaves like a textarea, but with some CSS magic
  // it grows with the input
  // shadowInput is only used for the visual effect of having the textarea grow
  const [shadowInput, setShadowInput] = useState(props.value ?? '');
  const [rows, setRows] = useState(props.value?.length ? 4 : 1);

  useEffect(() => {
    if (props.value != null) {
      setShadowInput(props.value + '\n' ?? '');
    }
  }, [props.value]);

  useEffect(() => {
    if (props.isExpanded == null) return;
    setRows(props.isExpanded ? 4 : 1);
  }, [props.isExpanded]);

  const onInput = e => {
    props.onInput?.(e);
    setShadowInput(e.target.value + '\n');
  };

  return (
    <div className="grow-container">
      <textarea
        {...props}
        ref={ref}
        className={(props.className ?? '') + ' form-control'}
        rows={rows}
        onInput={onInput}
        role="textbox"
      ></textarea>
      <div className="grow-box form-control">{shadowInput}</div>
    </div>
  );
});

GrowingTextAreaComponent.propTypes = {
  onInput: PropTypes.func,
  className: PropTypes.string,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  name: PropTypes.string,
  id: PropTypes.string,
  disabled: PropTypes.bool,
  isExpanded: PropTypes.bool,
};

export default GrowingTextAreaComponent;
