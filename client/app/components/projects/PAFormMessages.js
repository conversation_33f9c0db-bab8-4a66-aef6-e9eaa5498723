import { defineMessages } from 'react-intl';

export const PAFormMessages = defineMessages({
  OneManBusiness: {
    id: 'preannouncementDetails.pa_one_man_business',
    description: 'Label preannouncement one-man business',
    defaultMessage: 'Is the company a one-man business?',
  },
  OneManBusinessHelptext: {
    id: 'preannouncementDetails.pa_one_man_business_helptext',
    description: 'Label preannouncement one man business helptext',
    defaultMessage:
      'A one-man business is a company with one employee or a sole trader without employees.',
  },
  LabelNo: {
    id: 'preannouncementDetails.no',
    description: 'Label no',
    defaultMessage: 'No',
  },
  LabelYes: {
    id: 'preannouncementDetails.yes',
    description: 'Label yes',
    defaultMessage: 'Yes',
  },
  ForemanOnSite: {
    id: 'preannouncementDetails.pa_foreman_on_site',
    description: 'Label preannouncement is foreman on site',
    defaultMessage: 'Will there be a foreman on site?',
  },
  ForemanName: {
    id: 'preannouncementDetails.pa_foreman_first_name',
    description: 'Preannouncement foreman first name',
    defaultMessage: 'First name',
  },
  ForemanSurname: {
    id: 'preannouncementDetails.pa_foreman_last_name',
    description: 'Preannouncement foreman last name',
    defaultMessage: 'Last name',
  },
  ForemanPhone: {
    id: 'preannouncementDetails.pa_foreman_phone_number',
    description: 'Preannouncement foreman phone number',
    defaultMessage: 'Phone number',
  },
  ForemanEmail: {
    id: 'preannouncementDetails.pa_foreman_email',
    description: 'Preannouncement foreman email',
    defaultMessage: 'Email',
  },
  ForemanHeader: {
    id: 'preannouncementDetails.foreman_header',
    description: 'Label preannouncement foreman',
    defaultMessage: 'Foreman',
  },
  ForemanEmailError: {
    id: 'preannouncementDetails.pa_email_not_valid',
    description: 'Preannouncement foreman email invalid message',
    defaultMessage: 'Please enter a valid email address. ',
  },
  ContactEmail: {
    id: 'preannouncementDetails.pa_contact_info_email',
    description: 'Label preannouncement contact mail',
    defaultMessage: 'Primary email address',
  },
  ContactEmailHelpText: {
    id: 'preannouncementDetails.emailhelptext',
    description: 'Label preannouncement mail helper text',
    defaultMessage: 'Email address will be used for sending preannouncement notifications.',
  },
  ContactEmailConfirm: {
    id: 'preannouncementDetails.pa_contact_info_email_confirm',
    description: 'Label preannouncement mail confirm',
    defaultMessage: 'Confirm email address',
  },
  PAHeader: {
    id: 'preannouncementDetails.preannouncement_header',
    description: 'Label preannouncement',
    defaultMessage: 'Preannouncement',
  },
  PAStatus: {
    id: 'preannouncementDetails.pa_status',
    description: 'Label preannouncement status',
    defaultMessage: 'Status',
  },
  PAAssignedTo: {
    id: 'preannouncementDetails.pa_assigned_to',
    description: 'Label preannouncement assigned to',
    defaultMessage: 'Assigned to',
  },
  PAAssignedToDate: {
    id: 'preannouncementDetails.pa_assigned_to_date',
    description: 'Label preannouncement assigned to date',
    defaultMessage: 'Assigned to date',
  },
  PASubmittedDate: {
    id: 'preannouncementDetails.pa_submitted_date',
    description: 'Label preannouncement submitted date',
    defaultMessage: 'Submitted date',
  },
  PAReviewer: {
    id: 'preannouncementDetails.pa_reviewer',
    description: 'Label preannouncement reviewer',
    defaultMessage: 'Reviewer',
  },
  PAReceivedAt: {
    id: 'preannouncementDetails.pa_received',
    description: 'Label preannouncement received at',
    defaultMessage: 'Received',
  },
  PARejectedName: {
    id: 'preannouncementDetails.pa_rejected_name',
    description: 'Label preannouncement rejected by',
    defaultMessage: 'Rejected by',
  },
  PARejectedDate: {
    id: 'preannouncementDetails.pa_rejected_date',
    description: 'Label preannouncement rejected on',
    defaultMessage: 'Rejected date',
  },
  PAConfirmedName: {
    id: 'preannouncementDetails.pa_confirmed_name',
    description: 'Label preannouncement confirmed by',
    defaultMessage: 'Confirmed by',
  },
  PAConfirmedDate: {
    id: 'preannouncementDetails.pa_confirmed_date',
    description: 'Label preannouncement confirmed on',
    defaultMessage: 'Confirmation date',
  },
  ContactInfoHeader: {
    id: 'preannouncementDetails.contactinfo_header',
    description: 'Label contact information header',
    defaultMessage: 'Contact information',
  },
  InformantCustomerHeader: {
    id: 'preannouncementDetails.informant_customer_header',
    description: 'Label preannouncement informant customer',
    defaultMessage: 'Informant customer',
  },
  InformantHeader: {
    id: 'preannouncementDetails.informant_header',
    description: 'Label preannouncement informant',
    defaultMessage: 'Informant supplier',
  },
  InformantName: {
    id: 'preannouncementDetails.pa_informant',
    description: 'Label preannouncement informant name',
    defaultMessage: 'Name',
  },
  InformantEmail: {
    id: 'preannouncementDetails.pa_informant_mail',
    description: 'Label preannouncement informant mail',
    defaultMessage: 'Email address',
  },
  InformantPhone: {
    id: 'preannouncementDetails.pa_informant_phone',
    description: 'Label preannouncement informant phone',
    defaultMessage: 'Phone number',
  },
  ProjectInfoHeader: {
    id: 'preannouncementDetails.pa_project_info_header',
    description: 'Label preannouncement Project information',
    defaultMessage: 'Project information',
  },
  ProjectName: {
    id: 'preannouncementDetails.pa_project_name',
    description: 'Label preannouncement Project name',
    defaultMessage: 'Project name',
  },
  Client: {
    id: 'preannouncementDetails.pa_construction_client',
    description: 'Label preannouncement Construction client',
    defaultMessage: 'Construction client',
  },
  MainContractor: {
    id: 'preannouncementDetails.pa_main_contractor',
    description: 'Label preannouncement Main contractor',
    defaultMessage: 'Main contractor',
  },
  Contractor: {
    id: 'preannouncementDetails.pa_contractor',
    description: 'Label preannouncement Contractor',
    defaultMessage: 'Contractor',
  },
  Customer: {
    id: 'preannouncementDetails.pa_customer',
    description: 'Label preannouncement Customer',
    defaultMessage: 'Customer',
  },
  ContractInfoHeader: {
    id: 'preannouncementDetails.pa_contract_information_header',
    description: 'Label preannouncement Contract information',
    defaultMessage: 'Contract information',
  },
  ContractType: {
    id: 'preannouncementDetails.pa_contract_type',
    description: 'Label preannouncement Contract type',
    defaultMessage: 'Contract type',
  },
  ContractStartDate: {
    id: 'preannouncementDetails.pa_start_date',
    description: 'Label preannouncement Start date',
    defaultMessage: 'Start date',
  },
  ContractEndDate: {
    id: 'preannouncementDetails.pa_end_date',
    description: 'Label preannouncement End date',
    defaultMessage: 'End date',
  },
  ProfessionalWork: {
    id: 'preannouncementDetails.pa_professional_work',
    description: 'Label preannouncement Professional work',
    defaultMessage: 'Professional work',
  },
  CompanyInfoHeader: {
    id: 'preannouncementDetails.pa_company_information_header',
    description: 'Label preannouncement',
    defaultMessage: 'Company information',
  },

  CompanyName: {
    id: 'preannouncementDetails.pa_company_name',
    description: 'Label preannouncement Company name',
    defaultMessage: 'Company name',
  },

  BusinessId: {
    id: 'preannouncementDetails.pa_company_business_id',
    description: 'Label preannouncement Business ID',
    defaultMessage: 'Business ID',
  },

  VAT: {
    id: 'preannouncementDetails.pa_vat',
    description: 'Label preannouncement vat',
    defaultMessage: 'VAT',
  },

  RegistrationCountry: {
    id: 'preannouncementDetails.pa_country_of_regiser',
    description: 'Label preannouncement Country of registration',
    defaultMessage: 'Country of registration',
  },

  ActiveCards: {
    id: 'preannouncementDetails.pa_active_cards',
    description: 'Label preannouncement active cards',
    defaultMessage: 'Active cards',
  },

  CompanyStatus: {
    id: 'preannouncementDetails.pa_company_status',
    description: 'Label preannouncement company status',
    defaultMessage: 'Status',
  },

  HasPermPlace: {
    id: 'preannouncementDetails.pa_perm_place',
    description: 'Label preannouncement company to have a permanent place of business',
    defaultMessage: 'Is there a basis for the company to have a permanent place of business?',
  },

  CollectiveAgreement: {
    id: 'preannouncementDetails.pa_collective_agreement',
    description: 'Label preannouncement collective agreement',
    defaultMessage:
      // eslint-disable-next-line max-len
      "Is the company a member of an employers' organization or has the company signed a suspension agreement?.",
  },
  CollectiveAgreementName: {
    id: 'preannouncementDetails.pa_collective_agreement_name',
    description: 'Label preannouncement Which collective agreement',
    defaultMessage: 'Enter the name of the agreement',
  },
  CollectiveAgreementPlaceholder: {
    id: 'preannouncementDetails.pa_collective_agreement_placeholder',
    description: 'Placeholder of preannouncement collective agreement input',
    defaultMessage: 'Example: construction machinery agreement',
  },
  ContractLongerThanSixMonths: {
    id: 'preannouncementDetails.contracLongerThanSixMonths',
    description: 'warning text for long contract for foreign supplier',
    defaultMessage: 'Any contract period longer than 6 months requires a permanent establishment',
  },
  required: {
    id: 'supplierForm.field_is_required',
    description: 'Error message for required field left blank',
    defaultMessage: 'Required field.',
  },
  SwedishTaxNumber: {
    id: 'preannouncementDetails.swedishTaxNumber',
    description: 'Swedish tax number',
    defaultMessage: 'Swedish tax number',
  },
  swTaxNumberHelptext: {
    id: 'preannouncementDetails.swTaxNumberHelptext',
    description: 'Helptext for swedish tax number for other user',
    defaultMessage: 'Information registered by the supplier in the ID06 system.',
  },
  paCanConfirmOverride: {
    id: 'preannouncementDetails.paCanConfirmOverride',
    description: 'Label for company status report when report is not available',
    defaultMessage: 'Report could not be downloaded',
  },
  paCanConfirmWait: {
    id: 'preannouncementDetails.paCanConfirmWait',
    description: 'Label for company report when it is waited',
    defaultMessage: 'Report is downloading…',
  },
  paCanConfirmWaitHelpText: {
    id: 'preannouncementDetails.paCanConfirmWaitHelpText',
    description: 'Help message when company report is waited',
    defaultMessage:
      // eslint-disable-next-line max-len
      'The report is downloaded in the background. Close the window and try again in a few minutes.',
  },
  commentsHeader: {
    id: 'preannouncementDetails.comments_header',
    description: 'Header for comment section',
    defaultMessage: 'Comments',
  },
});

export default PAFormMessages;
