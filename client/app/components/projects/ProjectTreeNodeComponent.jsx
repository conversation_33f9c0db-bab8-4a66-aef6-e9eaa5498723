/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'

import addSubcontractorsMenuClicked from '../../actions/actionCreators/AddSubcontractorsMenuClicked';
import subcontractorEditMenuClicked from '../../actions/actionCreators/SubcontractorEditMenuClicked';
import moveSupplierMenuClicked from '../../actions/actionCreators/MoveSupplierMenuClicked';
import placeSupplierMenuClicked from '../../actions/actionCreators/PlaceSupplierMenuClicked';
import companyDetailsTreeMenuClicked from '../../actions/actionCreators/CompanyDetailsTreeMenuClicked';
import projectTreeCancelCurrentMove from '../../actions/actionCreators/ProjectTreeCancelCurrentMove';
import projectTreeUndoMove from '../../actions/actionCreators/ProjectTreeUndoMove';
import projectTreeOpenReport from '../../actions/actionCreators/ProjectTreeOpenReport';
import IdCardIconComponent from '../shared/IdCardIconComponent';
import StatusIconComponent from '../shared/StatusIconComponent';
import VisitorIconComponent from '../shared/VisitorIconComponent';
import CommentIconComponent from '../shared/CommentIconComponent';
import { SUPPLIER_LINKED, SUPPLIER_UNLINKED, SUPPLIER_VISITOR } from '../../Constants';
import NodeMenuComponent from './NodeMenuComponent';
import NodeMenuItem from './NodeMenuItem';
import { countChildren } from '../../helpers/ProjectTree';
import { SupplierRolesMessages, SUPPLIER } from './SupplierRoles';
import { stopPropagation } from '../../helpers/EventHandlers';
import { intlPropType } from '../i18n/IntlPropTypes';
/* eslint-enable max-len */

class ProjectTreeNodeComponent extends React.Component {
  static get defaultProps() {
    return {
      unlinked: false,
      ellipsis: false,
      expanded: false,
      moved: false,
      hasStatusIcon: true,
      hasAddAction: false,
      hasEditAction: false,
      hasMoveAction: false,
      hasPlaceAction: false,
      hasCancelAction: false,
      hasUndoAction: false,
      undoActionDisabled: false,
      disableContextMenu: false,
      disableExpandUpdate: false,
      hideStatuses: false,
      supplierPermissions: [],
    };
  }

  static get propTypes() {
    return {
      id: PropTypes.string,
      email: PropTypes.string,
      hasDetailsAction: PropTypes.bool,
      unlinked: PropTypes.bool,
      ellipsis: PropTypes.bool,
      expanded: PropTypes.bool,
      moved: PropTypes.bool,
      depth: PropTypes.number,
      status: PropTypes.oneOf(['stop', 'investigate', 'incomplete', 'attention', 'ok']),
      classNames: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
      name: PropTypes.node.isRequired,
      govOrgId: PropTypes.string,
      vatNumber: PropTypes.string,
      country: PropTypes.string,
      role: PropTypes.node,
      contractType: PropTypes.node,
      contractStartDate: PropTypes.string,
      contractEndDate: PropTypes.string,
      contractWorkAreas: PropTypes.array,
      isOneManCompany: PropTypes.bool,
      hasCollectiveAgreement: PropTypes.bool,
      collectiveAgreementName: PropTypes.string,
      contacts: PropTypes.arrayOf(PropTypes.object),
      childrenStatusCounts: PropTypes.objectOf(PropTypes.number),
      worstSubtreeStatus: PropTypes.oneOf(['stop', 'investigate', 'incomplete', 'attention', 'ok']),
      companyId: PropTypes.string,
      supplierId: PropTypes.string,
      supplierType: PropTypes.oneOf([SUPPLIER_LINKED, SUPPLIER_UNLINKED, SUPPLIER_VISITOR]),
      supplierPermissions: PropTypes.array,
      firstVisited: PropTypes.string,
      lastVisited: PropTypes.string,
      reportAvailable: PropTypes.bool,
      supplierRev: PropTypes.string,
      hasStatusIcon: PropTypes.bool,
      hasAddAction: PropTypes.bool,
      hasEditAction: PropTypes.bool,
      hasMoveAction: PropTypes.bool,
      hasPlaceAction: PropTypes.bool,
      hasCancelAction: PropTypes.bool,
      hasCommentAction: PropTypes.bool,
      hasUndoAction: PropTypes.bool,
      handleRemoveAction: PropTypes.func,
      // Display undo action but show it as disabled
      undoActionDisabled: PropTypes.bool,
      movedSupplierRole: PropTypes.string,
      children: PropTypes.node,
      disableContextMenu: PropTypes.bool,
      // eslint-disable-next-line react/no-unused-prop-types
      disableExpandUpdate: PropTypes.bool,
      hideStatuses: PropTypes.bool,
      hasPreannounceAction: PropTypes.bool,
      preannouncementId: PropTypes.string,
      hasCreateNewPreannouncementAction: PropTypes.bool,
      hasComment: PropTypes.bool,
      hasUnreadComment: PropTypes.bool,
      intl: intlPropType.isRequired
    };
  }

  constructor(props) {
    super(props);
    this.state = {
      isExpanded: props.expanded,
    };
  }

  // TODO: get rid of UNSAFE React lifecycle method below
  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(nextProps) {
    // BOL-2331: We don't want to change expanded state when doing anything else
    // but expanding all tree before print preview
    if (!nextProps.disableExpandUpdate && nextProps.expanded !== this.state.isExpanded) {
      this.setState({
        isExpanded: nextProps.expanded,
      });
    }
  }

  hasChildren() {
    return React.Children.count(this.props.children) > 0;
  }

  descendantCount() {
    return countChildren(this.props.childrenStatusCounts);
  }

  nodeRole() {
    return this.props.role && this.props.role !== SUPPLIER ? this.props.role : null;
  }

  hasVisitorData() {
    return (
      this.props.supplierType === SUPPLIER_VISITOR &&
      (this.props.country || this.props.firstVisited || this.props.lastVisited)
    );
  }

  isTwoLine() {
    return this.nodeRole() || this.hasVisitorData();
  }

  toggleExpand(event) {
    event.preventDefault();
    if (this.props.disableContextMenu) {
      return false;
    }
    this.setState({
      isExpanded: !this.state.isExpanded,
    });
    return false;
  }

  renderExpandHandle() {
    if (!this.hasChildren()) return null;
    const cssClasses = classNames({
      'btn-branch-toggle': true,
      expanded: this.state.isExpanded,
    });
    return <div className={cssClasses} onClick={this.toggleExpand.bind(this)} />;
  }

  renderStatusIcon() {
    if (!this.props.hasStatusIcon) return null;

    if (this.props.supplierType === SUPPLIER_VISITOR && this.props.reportAvailable !== true)
      return <VisitorIconComponent />;

    if (!this.props.status) {
      return <VisitorIconComponent />;
    }

    return <StatusIconComponent status={this.props.status} />;
  }

  renderCommentsIcon() {
    if (!this.props.hasComment) return null;

    const handleClick = (e) => {
      stopPropagation(e);
      return companyDetailsTreeMenuClicked(
        this.contextBlockTargetRef,
        this.props.companyId,
        this.props.name,
        this.props.supplierId,
      );
    };

    return (
      <span
        onClick={handleClick}
        style={{ cursor: 'pointer' }}
        data-testid="comments-icon"
      >
        <CommentIconComponent
          has_comment={this.props.hasComment}
          has_unread_comment={this.props.hasUnreadComment}
        />
      </span>
    );
  }

  renderMenu() {
    const addCallback = e => {
      stopPropagation(e);
      return addSubcontractorsMenuClicked(this.contextBlockTargetRef, {
        companyName: this.props.name,
        supplierId: this.props.supplierId,
        supplierType: this.props.supplierType,
        isRoot: false,
      });
    };
    const editCallback = e => {
      stopPropagation(e);
      return subcontractorEditMenuClicked(this.contextBlockTargetRef, {
        companyName: this.props.name,
        companyId: this.props.companyId,
        govOrgId: this.props.govOrgId,
        vatNumber: this.props.vatNumber,
        country: this.props.country,
        supplierId: this.props.supplierId,
        supplierType: this.props.supplierType,
        supplierRev: this.props.supplierRev,
        role: this.props.role,
        contractType: this.props.contractType,
        contractStartDate: this.props.contractStartDate,
        contractEndDate: this.props.contractEndDate,
        contractWorkAreas: this.props.contractWorkAreas,
        isOneManCompany: this.props.isOneManCompany,
        hasCollectiveAgreement: this.props.hasCollectiveAgreement,
        collectiveAgreementName: this.props.collectiveAgreementName,
        contacts: this.props.contacts,
        email: this.props.email,
        isRoot: false,
        depth: this.props.depth,
        permissions: this.props.supplierPermissions,
        preannouncementId: this.props.preannouncementId,
      });
    };
    const createNewPaCallback = e => {
      stopPropagation(e);
      return subcontractorEditMenuClicked(
        this.contextBlockTargetRef,
        {
          companyName: this.props.name,
          companyId: this.props.companyId,
          govOrgId: this.props.govOrgId,
          vatNumber: this.props.vatNumber,
          country: this.props.country,
          supplierId: this.props.supplierId,
          supplierType: this.props.supplierType,
          supplierRev: this.props.supplierRev,
          role: this.props.role,
          contractType: this.props.contractType,
          contractStartDate: this.props.contractStartDate,
          contractEndDate: this.props.contractEndDate,
          contractWorkAreas: this.props.contractWorkAreas,
          isOneManCompany: this.props.isOneManCompany,
          hasCollectiveAgreement: this.props.hasCollectiveAgreement,
          collectiveAgreementName: this.props.collectiveAgreementName,
          contacts: this.props.contacts,
          email: this.props.email,
          isRoot: false,
          depth: this.props.depth,
          permissions: this.props.supplierPermissions,
          preannouncementId: this.props.preannouncementId,
        },
        true
      );
    };
    const moveCallback = e => {
      stopPropagation(e);
      return moveSupplierMenuClicked(
        this.props.supplierId,
        this.props.supplierRev,
        this.props.supplierType,
        this.props.role
      );
    };
    const placeCallback = e => {
      stopPropagation(e);
      return placeSupplierMenuClicked(
        this.props.supplierId,
        this.props.supplierType,
        this.props.movedSupplierRole,
        this.contextBlockTargetRef
      );
    };
    const removeCallback = e => {
      stopPropagation(e);
      return (
        this.props.handleRemoveAction && this.props.handleRemoveAction(this.contextBlockTargetRef)
      );
    };
    const detailsCallback = e => {
      stopPropagation(e);
      return companyDetailsTreeMenuClicked(
        this.contextBlockTargetRef,
        this.props.companyId,
        this.props.name,
        this.props.supplierId,
      );
    };
    const viewReportCallback = e => {
      stopPropagation(e);
      return projectTreeOpenReport(this.props.companyId);
    };
    const cancelMoveCallback = e => {
      stopPropagation(e);
      return projectTreeCancelCurrentMove();
    };

    const hasReport = this.props.reportAvailable;

    return (
      <NodeMenuComponent disableContextMenu={this.props.disableContextMenu}>
        <NodeMenuItem
          visible={this.props.hasPreannounceAction}
          className="dropdown-preannounces-supplier"
          onClick={addCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.preannounceSupplier"
            description="Preannounce subsupplier menu item"
            defaultMessage="Preannounce subsupplier"
          />
        </NodeMenuItem>
        <NodeMenuItem
          visible={this.props.hasAddAction && !this.props.hasPreannounceAction}
          className="dropdown-add-supplier"
          onClick={addCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.addSupplier"
            description="Add supplier project tree item menu entry"
            defaultMessage="Add supplier"
          />
        </NodeMenuItem>
        <NodeMenuItem
          visible={this.props.hasEditAction}
          className="dropdown-edit-supplier"
          onClick={editCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.editSupplier"
            description="Edit supplier project tree item menu entry"
            defaultMessage="Edit supplier"
          />
        </NodeMenuItem>
        <NodeMenuItem
          visible={this.props.hasMoveAction}
          className="dropdown-move-supplier"
          onClick={moveCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.moveSupplier"
            description="Move supplier project tree item menu entry"
            defaultMessage="Move supplier"
          />
        </NodeMenuItem>
        <NodeMenuItem
          visible={this.props.hasPlaceAction}
          className="dropdown-place-supplier"
          onClick={placeCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.placeSupplier"
            description="Place supplier project tree item menu entry"
            defaultMessage="Place supplier"
          />
        </NodeMenuItem>
        <NodeMenuItem
          visible={this.props.hasCancelAction}
          className="dropdown-cancel-move"
          onClick={cancelMoveCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.cancelMove"
            description="Cancel current supplier move in project tree"
            defaultMessage="Cancel move"
          />
        </NodeMenuItem>
        <NodeMenuItem
          visible={!!this.props.handleRemoveAction}
          className="dropdown-remove-supplier"
          onClick={removeCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.removeSupplier"
            description="Remove supplier in project tree"
            defaultMessage="Remove supplier"
          />
        </NodeMenuItem>
        <NodeMenuItem
          visible={this.props.hasCommentAction}
          className="dropdown-write-comment"
          onClick={detailsCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.writeComment"
            description="Write comment for supplier"
            defaultMessage="Write comment"
          />
        </NodeMenuItem>
        <NodeMenuItem
          visible={this.props.hasCreateNewPreannouncementAction}
          className="dropdown-create-new-preannouncement"
          onClick={createNewPaCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.createNewPreannouncement"
            description="Create new preannonucement menu item"
            defaultMessage="Create new preannouncement"
          />
        </NodeMenuItem>
        <NodeMenuItem
          visible={this.props.hasDetailsAction}
          className="dropdown-company-details"
          onClick={detailsCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.companyDetails"
            description="Show company details popup"
            defaultMessage="View supplier"
          />
        </NodeMenuItem>
        <NodeMenuItem
          visible={this.props.hasDetailsAction && hasReport}
          className="dropdown-company-report"
          onClick={viewReportCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.viewReport"
            description="Show company report"
            defaultMessage="View report"
          />
        </NodeMenuItem>
      </NodeMenuComponent>
    );
  }

  renderChildrenStatuses() {
    const statusesToDisplay = ['stop', 'investigate', 'incomplete', 'attention'];
    if (!this.props.childrenStatusCounts || this.props.unlinked) return null;
    const statusIcons = [];
    statusesToDisplay.forEach(status => {
      const count = this.props.childrenStatusCounts[status];
      if (!count) return;

      const badge = count > 1 ? <span className="status-text">{count}</span> : null;
      statusIcons.push(
        <a key={status}>
          {badge}
          <StatusIconComponent status={status} />
        </a>
      );
    });

    if (statusIcons.length) {
      return <div className="status-icons">{statusIcons}</div>;
    }

    return null;
  }

  renderVisitorCards() {
    if (this.props.supplierType === SUPPLIER_VISITOR)
      return (
        <div className="id-card-icon">
          <IdCardIconComponent />
        </div>
      );
    return null;
  }

  renderMovedMessage() {
    if (!this.props.moved) {
      return null;
    }
    return (
      <div className="node-note pointer-events-none">
        <FormattedMessage
          id="projects.node.beingMoved"
          description="Project tree supplier node text indicating node is selected for move"
          defaultMessage="Selected for move"
        />
      </div>
    );
  }

  renderUndoMove() {
    if (!this.props.hasUndoAction) {
      return null;
    }
    const undoCallback = this.props.undoActionDisabled
      ? null
      : () => projectTreeUndoMove(this.props.supplierId);
    return (
      <a
        className={classNames({
          'node-undo': true,
          'node-action': true,
          'node-action--disabled': this.props.undoActionDisabled,
        })}
        onClick={undoCallback}
        href="#"
      >
        <i className="fa fa-undo fa-pr--bol" aria-hidden="true" />
        <FormattedMessage
          id="projects.node.undoText"
          description="Undo move in project tree"
          defaultMessage="Undo move"
        />
      </a>
    );
  }

  renderDescendantCount() {
    if (!this.hasChildren() || !this.props.childrenStatusCounts) return null;
    return <span className="descendant-count">({this.descendantCount()})</span>;
  }

  renderOrgIds() {
    const vatNumber = this.props.vatNumber ? this.props.vatNumber : null;
    const { govOrgId } = this.props;
    const seprator = ' | ';
    const clsNames = this.props.supplierType !== SUPPLIER_VISITOR ? 'node-orgid' : null;
    return (
      <span className={clsNames}>
        {govOrgId && govOrgId}
        {govOrgId && vatNumber && seprator}
        {vatNumber && vatNumber}
      </span>
    );
  }

  renderCountry() {
    if (!this.props.country || this.props.supplierType !== SUPPLIER_VISITOR) return null;
    return <span className="country"> ({this.props.country})</span>;
  }

  renderVisited() {
    if (!this.props.firstVisited) return null;
    return (
      <span className="node-visited">
        &ensp;
        {this.props.firstVisited == this.props.lastVisited
          ? this.renderVisitDate()
          : this.renderVisitPeriod()}
      </span>
    );
  }

  renderVisitDate() {
    return (
      <span className="node-visit-info">
        <FormattedMessage
          id="projects.node.visitDate"
          description="Label for the date of a single visit"
          defaultMessage="Visit date"
        />
        : {this.props.lastVisited}
      </span>
    );
  }

  renderVisitPeriod() {
    if (!this.props.lastVisited) return null;
    return (
      <span className="node-visit-info">
        <FormattedMessage
          id="projects.node.visitPeriod"
          description="Label for the period of visits"
          defaultMessage="Visiting period"
        />
        : {this.props.firstVisited} - {this.props.lastVisited}
      </span>
    );
  }

  renderCompanyName() {
    const role = this.nodeRole();
    const { formatMessage } = this.props.intl;
    const seprator = ' | ';
    return (
      <div
        className={classNames({
          left: true,
          'node-content': true,
          thick: this.isTwoLine(),
          grow: true,
          'pointers-events-none': true,
        })}
      >
        <span className="node-title">
          {this.props.name}
          {this.renderDescendantCount()}
          {this.props.hasComment && <span style={{ marginLeft: '8px' }} />}
          {this.renderCommentsIcon()}
        </span>
        <span className="node-role">
          {role && (
            <span>
              <span>{formatMessage(SupplierRolesMessages[role])}</span>
              <span className="node-orgid">{seprator}</span>
            </span>
          )}
          {this.renderOrgIds()}
          {this.renderCountry()}
          {this.renderVisited()}
        </span>
      </div>
    );
  }

  renderNodeActions() {
    if (!this.props.unlinked || !this.props.hasAddAction) return null;

    const addCallback = () =>
      addSubcontractorsMenuClicked(this.contextBlockTargetRef, {
        companyName: this.props.name,
        supplierId: this.props.supplierId,
        supplierType: this.props.supplierType,
        isRoot: false,
      });
    return (
      <a
        className="add-supplier node-action action-link"
        onClick={!this.props.disableContextMenu && addCallback}
        href="#"
      >
        <i className="fa fa-plus fa-pr--bol" aria-hidden="true" />
        <FormattedMessage
          id="projects.node.addSingleSupplier"
          description="Add single supplier action in node title"
          defaultMessage="Add new supplier"
        />
      </a>
    );
  }

  renderNode() {
    const cssClasses = classNames({
      'tree-node': true,
      'ellipsis-node': this.props.ellipsis,
      'moved-node': this.props.moved,
      'has-children': this.hasChildren(),
    });
    const cssNodeContent = classNames({
      'node-content': true,
      'hide-statuses': this.props.hideStatuses,
    });
    return (
      <div
        ref={targetRef => {
          this.contextBlockTargetRef = targetRef;
          this.contextBlockTargetName = this.props.name;
          this.contextBlockTargetId = this.props.id;
        }}
        className={cssClasses}
        data-depth={this.props.depth}
        data-worst-subtree-status={this.props.worstSubtreeStatus}
        onClick={this.toggleExpand.bind(this)}
      >
        <div className={cssNodeContent}>
          {this.renderStatusIcon()}
        </div>
        {this.renderCompanyName()}
        <div className={cssNodeContent}>
          {this.renderMenu()}
          {this.renderChildrenStatuses()}
          {this.renderVisitorCards()}
          {this.renderNodeActions()}
          {this.renderMovedMessage()}
          {this.renderUndoMove()}
        </div>
      </div>
    );
  }

  render() {
    const cssClasses = classNames(this.props.classNames, {
      parent: this.hasChildren(),
      expanded: this.state.isExpanded,
    });
    return (
      <li className={cssClasses}>
        {this.renderExpandHandle()}
        {this.renderNode()}
        {this.state.isExpanded && this.hasChildren() && <ul>{this.props.children}</ul>}
      </li>
    );
  }
}

export default injectIntl(ProjectTreeNodeComponent);
