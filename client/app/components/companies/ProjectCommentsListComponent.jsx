import React, { useState, useEffect, useRef } from 'react';
import { injectIntl, defineMessages } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage';
import StoreSubscription from '../../helpers/StoreSubscription';
import { commentsStore } from '../../stores/Stores';
import Spinner from '../shared/Spinner';
import ProjectCommentsDeleteModalComponent from './ProjectCommentsDeleteModalComponent';
import ProjectCommentsDisplayComponent from './ProjectCommentsDisplayComponent';
import updateSupplier from '../../actions/actionCreators/UpdateSupplier';
import companyProjectCommentsMarkRead
  from '../../actions/actionCreators/CompanyProjectCommentsMarkRead';

const messages = defineMessages({
  deleteFailed: {
    id: 'comments.deleteFailed',
    description: 'Label for failed delete action',
    defaultMessage: 'Failed to delete comment.',
  },
});
const ProjectCommentsListComponent = injectIntl(props => {
  const { formatMessage } = props.intl;
  const [state, setState] = useState(commentsStore.getState());
  const commentsMarkedRead = useRef(new Set());


  useEffect(() => {
    const storeChanged = storeState => {
      setState({
        comments: storeState.comments,
        comments_loading: storeState.comments_loading,
        comments_failed: storeState.comments_failed,
        supplier_id: storeState.supplier_id,
        comment_delete_id: storeState.comment_delete_id,
        comment_delete_failed: storeState.comment_delete_failed,
      });
    };

    const storeSubscription = new StoreSubscription(commentsStore, storeChanged);
    storeSubscription.activate();

    return () => {
      storeSubscription.deactivate();
    };
  }, []);

  useEffect(() => {
    if (state.comments && state.comments.length > 0) {
      const unreadCommentIds = state.comments
        .filter(c => !c.data.is_read && !commentsMarkedRead.current.has(c.data.id))
        .map(c => c.data.id);
      if (unreadCommentIds.length > 0) {
        companyProjectCommentsMarkRead(state.supplier_id, unreadCommentIds);
        unreadCommentIds.forEach(id => commentsMarkedRead.current.add(id));
      }
    }
    return () => {
      if (commentsMarkedRead.current.size > 0) {
        updateSupplier(state.supplier_id, {
          has_unread_comment: false,
        });
      }
    };
  }, [state.comments]);


  let { comments, comments_loading, comments_failed, comment_delete_id, comment_delete_failed } =
    state;

  let content;
  if (comments_loading) {
    content = <Spinner />;
  } else if (comments_failed) {
    content = (
      <FormattedMessage
        id="comments.loadingFailed"
        description="Failed to load comments"
        defaultMessage="Failed to load comments"
      />
    );
  } else {
    content = <ProjectCommentsDisplayComponent comments={comments || []} readOnly={false} />;
  }

  return (
    <>
      {comment_delete_failed && (
        <span className="d-flex justify-content-center alert-danger mb-4">
          {formatMessage(messages.deleteFailed)}
        </span>
      )}
      {content}
      {comment_delete_id && <ProjectCommentsDeleteModalComponent />}
    </>
  );
});

export default ProjectCommentsListComponent;
