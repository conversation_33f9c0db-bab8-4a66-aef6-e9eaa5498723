import React, { useState, useEffect } from 'react';
import { injectIntl, defineMessages } from 'react-intl';
import FormattedMessage from '../i18n/FormattedMessage'
import StoreSubscription from '../../helpers/StoreSubscription';
import { commentsStore } from '../../stores/Stores';
import ModalContextForm from '../projects/ModalContextForm';
import companyProjectCommentsCloseDeleteConfirmation
  from '../../actions/actionCreators/CompanyProjectCommentsCloseDeleteConfirmation';
import companyProjectCommentsDelete
  from '../../actions/actionCreators/CompanyProjectCommentsDelete';

const messages = defineMessages({
  cancel: {
    id: 'comments.cancel',
    description: 'Label for cancel action',
    defaultMessage: 'Cancel',
  },
  delete: {
    id: 'comments.delete',
    description: 'Label for delete action',
    defaultMessage: 'delete',
  },
  deleteWarning: {
    id: 'comments.deleteWarning',
    description: 'Label for delete warning',
    defaultMessage: 'Deleted comments cannot be restored.',
  },
  deleteWarningTitle: {
    id: 'comments.deleteWarningTitle',
    description: 'Label for delete warning title',
    defaultMessage: 'Are you sure you want to delete this comment?',
  },
});

const ProjectCommentsDeleteModalComponent = injectIntl((/*props*/) => {
  const [state, setState] = useState(commentsStore.getState());

  useEffect(() => {
    const storeChanged = (storeState) => {
      setState({
        comment_delete_id: storeState.comment_delete_id,
        supplier_id: storeState.supplier_id,
      });
    };

    const storeSubscription = new StoreSubscription(commentsStore, storeChanged);
    storeSubscription.activate();

    return () => {
      storeSubscription.deactivate();
    };
  }, []);

  useEffect(() => {
    function handleEscKey(e) {
      if (e.key === 'Escape') {
        e.stopImmediatePropagation();
        companyProjectCommentsCloseDeleteConfirmation();
      }
    }

    document.addEventListener('keyup', handleEscKey, true);

    return () => {
      document.removeEventListener('keyup', handleEscKey, true);
    };
  }, []);



  const handleClose = (e) => {
    e.preventDefault();
    companyProjectCommentsCloseDeleteConfirmation();
  };

  const handleDelete = async (e) => {
    e.preventDefault();
    companyProjectCommentsDelete(state.supplier_id, state.comment_delete_id);
  };

  return <div
    className="modal context-container comment-delete-modal"
    tabIndex="-1"
    role="dialog"
  >
    <ModalContextForm
      childComponent={
        <div>
          <div className='comment-delete-warning'>
            <FormattedMessage
              className="modal-title"
              id={messages.deleteWarning.id}
              description={messages.deleteWarning.description}
              defaultMessage={messages.deleteWarning.defaultMessage}
            />
          </div>
          <div className="col-md-12 d-flex justify-content-center">
            <button
              className="btn btn-sm btn-primary mr-3"
              role="button"
              onClick={handleDelete}
            >
              <FormattedMessage
                id={messages.delete.id}
                description={messages.delete.description}
                defaultMessage={messages.delete.defaultMessage}
              />
            </button>
            <a
              href="#"
              className="btn btn-sm btn-link"
              onClick={handleClose}
            >
              <FormattedMessage
                id={messages.cancel.id}
                description={messages.cancel.description}
                defaultMessage={messages.cancel.defaultMessage}
              />
            </a>
          </div>
        </div>
      }
      handleClose={handleClose}
      title={<FormattedMessage
        id={messages.deleteWarningTitle.id}
        description={messages.deleteWarningTitle.description}
        defaultMessage={messages.deleteWarningTitle.defaultMessage}
      />}
      doubleColumnWidth={false}
      outerClasses="context-block border"
    />
  </div>
});

export default ProjectCommentsDeleteModalComponent;
