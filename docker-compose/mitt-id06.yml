# This config runs all services needed for core except Bol
include:
  - networks.yml
services:
  haproxy:
    extends:
      file: docker-compose.yml
      service: haproxy
    environment:
      QVARN_AUTH_TOKEN_ISSUER: https://auth-core-alpha.id06.se
      QVARN_AUTH_TOKEN_JWKS_URL: https://auth-core-alpha.id06.se/oxauth/restv1/jwks
      QVARN_AUTH_TOKEN_VALIDATION_KEY: auto
      QVARN_AUTH_PROXY_TO: https://auth-core-alpha.id06.se/oxauth/restv1/token
      QVARN_LOG: stdout-oneline
      QVARN_DATABASE_HOST: qvarn-postgres
      QVARN_DATABASE_NAME: qvarn
      QVARN_DATABASE_USER: qvarn
      QVARN_DATABASE_PASSWORD: qvarn

  host-container:
    container_name: mitt-id06-host-container
    image: crvaultitdevtest.azurecr.io/mitt-id06-host-container:latest
    ports:
      - "8080:8080"

  entry:
    container_name: mitt-id06-entry
    # Change tag to `unstable` for latest develop build
    image: crvaultitdevtest.azurecr.io/mitt-id06-entry:latest
    # Change to this for using image from `docker compose build`` in mitt-id06 repo
    # image: mitt-id06-entry:dev
    ports:
      - "3000:3000"

  host-container-with-mock:
    container_name: mitt-id06-host-container-with-mock
    # Change tag to `unstable` for latest develop build
    image: crvaultitdevtest.azurecr.io/mitt-id06-host-container:latest
    # Change to this for using image from `docker compose build`` in mitt-id06 repo
    # image: mitt-id06-host-container:dev
    ports:
      - "8080:8080"
    environment:
      - PIMCORE_HOST=http://localhost:5001
    healthcheck:
      test: "wget -qO- http://localhost:8080/entry/login | grep -q 'Mitt ID06'"

  core-mock-sysapi:
    container_name: core-mock-sysapi
    image: crvaultitdevtest.azurecr.io/core-system-api-mock:latest
    ports:
      - "5001:5001"
    healthcheck:
      test: "wget -q -T 10 -O /dev/null http://127.0.0.1:5001/ping"
    networks:
      - customnet
    environment:
      - CORE_SYSTEM_API_MOCK_APP_LOG_LEVELS=fatal,error,warn,log
      # Switch to this for more verbose output
      # - CORE_SYSTEM_API_MOCK_APP_LOG_LEVELS=fatal,error,warn,log,debug

  bol-data-api:
    environment:
      # Replace URL when running with core
      BOLDATAAPI_AUTH_GLUU_ADDRESSES: https://auth-core-alpha.id06.se
      BOLDATAAPI_FEATURE_FLAGS_CORE: "true"

  core-sync-fixtures:
    extends:
      file: app-base.yml
      service: app-base-core-mock
    command: >
      sh -c "
        bolsyncfixtures --config /etc/bolfak/bolfak.cfg &&
        bolsyncusers --config /etc/bolfak/bolfak.cfg
      "
    depends_on:
      bol-data-api:
        condition: service_healthy
      core-mock-sysapi:
        condition: service_started
    volumes:
      - ../server/bolfak:/usr/local/lib/python3.11/dist-packages/bolfak

  # creates the users in gluu alpha
  core-sync-glue-alpha:
    extends:
      file: app-base.yml
      service: app-base-core
    command: ["sh", "-c", "bolsyncusersgluu --config /etc/bolfak/bolfak.cfg"]
    depends_on:
      bol-data-api:
        condition: service_healthy
    volumes:
      - ../server/bolfak:/usr/local/lib/python3.11/dist-packages/bolfak
