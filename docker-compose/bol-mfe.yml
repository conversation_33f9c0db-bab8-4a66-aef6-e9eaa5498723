# This config runs BOL with core and it's dependancies
include:
  - networks.yml
services:

  rabbitmq:
    image: rabbitmq
    ports:
      - "5672:5672"
    networks:
      - customnet
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]

  celery:
    extends:
      file: app-base.yml
      service: app-base-core-mock
    command: ["celery", "-A", "bolfak", "worker", "--bol-config-file", "/etc/bolfak/bolfak.cfg"]
    environment:
      CELERY_BROKER_URL: amqp://rabbitmq
      CELERY_TASK_DEFAULT_QUEUE: celery-que-bol-local
      CELERY_TASK_DEFAULT_EXCHANGE: celery-exc-bol-local
      BOL_CELERY_READINESS_FILE: /tmp/celery-ready
    healthcheck:
      test: "test -e /tmp/celery-ready"
    networks:
      - customnet
    depends_on:
      db:
        condition: service_started
      bol-data-api:
        condition: service_healthy
      haproxy:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy

  redis:
    restart: always
    image: redis:latest
    # command: redis-server --requirepass redispass --loglevel debug
    command: redis-server --requirepass redispass
    ports:
      - "6379:6379"
    networks:
      - customnet
    healthcheck:
      test: ["CMD", "redis-cli","ping"]

  app-core-mock:
    extends:
      file: app-base.yml
      service: app-base-core-mock
    environment:
      CELERY_BROKER_URL: amqp://rabbitmq
      CELERY_TASK_DEFAULT_QUEUE: celery-que-bol-local
      CELERY_TASK_DEFAULT_EXCHANGE: celery-exc-bol-local
    depends_on:
      redis:
        condition: service_started
      db:
        condition: service_started
      qvarn:
        condition: service_started
      bol-data-api:
        condition: service_healthy
      core-mock-sysapi:
        condition: service_started
      entry:
        condition: service_started
    ports:
      - "8082:8080"
    healthcheck:
      test: "wget -q -T 10 -O /dev/null http://localhost:8080/api/status"
      interval: 30s
    networks:
      - customnet

