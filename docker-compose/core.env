# Config to run BOL in containers towards Core Mock API
frontend_url=http://localhost:8080/
backend_url=http://localhost:8082/api/
standalone_url=http://localhost:8082/
service_provider=id06
auth_data_caching_duration_in_seconds=5
environment_config=/etc/bolfak/env-config.yaml
person_org_contract_type=user_account
org_contract_type=customership
bda_base_url=http://bol-data-api:8000
client_id=28a036c2-c64f-41a9-9758-db4cea4a5656
client_secret=6HzvULKONTIdci26JCVi7d0n95KE4BJKk7NYiyQf
gluu_base_url=https://auth-core-alpha.id06.se
contract_api_base_url=http://contract-api
session_type=redis
redis_dsn=redispass@redis:6379/0
session_cookie_domain=
session_encrypt_key=very
session_validate_key=secret
report_version_id06=
bulkimport_data_provider=bolfak.statusreports.sandbox.SandboxInfoProvider
report_data_provider=bolfak.statusreports.sandbox.Sandbox
report_swedish_data_provider=bolfak.statusreports.sandbox.Sandbox
report_foreign_data_provider=bolfak.statusreports.sandbox.Sandbox
creditsafe_username=
creditsafe_password=
creditsafe_symmetric_secret_key=
creditsafe_ggs_wsdl_username=
creditsafe_ggs_wsdl_password=
autoaccount_url=
autoaccount_username=
autoaccount_password=
autoaccount_request_package=
autoaccount_use_testing_org_gov_org_id=
autoaccount_testing_org_gov_org_id=
autoaccount_id06_gov_org_id=
change_user_details_url=
default_storage=qvarn
skatteverket_client_pem=
skatteverket_ca_pem=
worker_process_count=1
session_cookie_secure=false
qvarn_threadpool_size=20
log_level=DEBUG
swagger_api=true
bol_permission=bolagsdeklaration_user
feature_flag_extended_report=true
feature_flag_pagination=true
feature_flag_visitors=true
feature_flag_celery_for_sendgrid=true
shared_cookie_domain=localhost
i18_variant=id06
feature_flag_bda_client=true
feature_flag_bda_company_list=true
feature_flag_web_reports=false
feature_flag_project_report=true
feature_flag_bda_project_suppliers=true
feature_flag_core_mitt_id06=true
feature_flag_cqpoc_client=false
feature_flag_cqpoc_get_companies=false
feature_flag_pre_announcements=true
feature_flag_pa_form_checkbox_disabled=true
feature_flag_non_paed_suppliers=true
feature_flag_contract_api_creditsafe_contract=false
feature_flag_user_account_api=false
feature_flag_add_project_client=false
feature_flag_block_project_client=false
feature_flag_skip_pa_reg_step=true
feature_flag_create_and_activate_cs_accounts=false
feature_flag_person_id_for_project_users=true
feature_flag_skip_gluu_login=true
feature_flag_dependency_request_cache=yes
# Email sending via Sendgrid
sendgrid_sender='ID06 alpha <<EMAIL>>'
sendgrid_api_key=*********************************************************************
test_api=true
# Dependency request cache
cache_time_to_live=15
cache_max_size=5000
