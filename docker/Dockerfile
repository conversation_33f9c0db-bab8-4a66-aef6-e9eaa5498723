FROM crvaultitdevtest.azurecr.io/debian:bookworm
  # bookworm is the codename for Debian 12
MAINTAINER POV Team <<EMAIL>>

RUN set -x \
  && apt-get update -qq \
  && apt-get dist-upgrade -qq -y \
  && DEBIAN_FRONTEND=noninteractive apt-get install -y \
        nginx \
        nginx-extras \
        uwsgi \
        uwsgi-plugin-python3 \
        python3-uwsgidecorators \
        cron \
        curl \
        wget \
        sudo \
        busybox-syslogd \
        postgresql-client \
        python3-pip \
  && apt-get autoremove -y \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Debian packaging is hard, install Python packages directly and hope
COPY build/wheels/*.whl /opt/bol-packages/
ENV PIP_BREAK_SYSTEM_PACKAGES=1
RUN pip3 install -U pip
RUN pip3 install newrelic
RUN pip3 install sendgrid==6.10.0
RUN pip3 install /opt/bol-packages/bolfak*.whl -f /opt/bol-packages --no-index --pre -I && \
    python3 -c 'import bolfak' && \
    rm -rf /opt/bol-packages

COPY build/client /srv/bolfak/www

COPY bolfak.cfg /etc/bolfak/bolfak.cfg
COPY uwsgi.ini /etc/uwsgi/apps-enabled/bolagsfakta.ini
COPY nginx.conf /etc/nginx/nginx.conf
COPY statusreports.cfg /etc/bolfak/statusreports.cfg
COPY notifications.cfg /etc/bolfak/notifications.cfg
COPY monitoring.cfg /etc/bolfak/monitoring.cfg
COPY newrelic.ini /etc/bolfak/newrelic.ini

# Cron setup is based on https://github.com/Ekito/docker-cron/
COPY crontab.* /
RUN chmod 644 /crontab.*
RUN rm -f /etc/cron.daily/* /etc/cron.weekly/* /etc/cron.monthly/* /etc/cron.hourly/*
# create empty file so pam_env(cron:session) won't spam errors
RUN touch /etc/default/locale

COPY run.sh /run.sh
COPY cron.sh /cron.sh
RUN chmod +x /run.sh /cron.sh

ARG build=1
ENV build=$build \
    PYTHONUNBUFFERED=1 \
    frontend_url=http://localhost:8080/ \
    backend_url=http://localhost:8080/api/ \
    service_portal_url=http://localhost:8081/ \
    company_registry_url=BOL_COMPANY_REGISTRY_URL \
    companies_url=BOL_COMPANIES_URL \
    service_provider=id06 \
    auth_data_caching_duration_in_seconds=5 \
    environment_config=/etc/bolfak/bolfak.cfg \
    person_org_contract_type=tilaajavastuu_account \
    org_contract_type=tilaajavastuu_subscription \
    bda_base_url=BOL_BDA_BASE_URL \
    default_storage=qvarn \
    qvarn_base_url=BOL_QVARN_ADDRESS \
    qvarnlike_url_for_orgs=BOL_QVARNLIKE_URL_FOR_ORGS \
    qvarnlike_url_for_cards=BOL_QVARNLIKE_URL_FOR_CARDS \
    qvarnlike_url_for_persons=BOL_QVARNLIKE_URL_FOR_PERSONS \
    coresysapi_base_url=BOL_CORESYSAPI_BASE_URL \
    verify_requests=yes \
    client_id=BOL_CLIENT_ID \
    client_secret=BOL_CLIENT_SECRET \
    gluu_base_url=BOL_GLUU_ADDRESS \
    session_type=BOL_SESSION_TYPE \
    redis_dsn=BOL_REDIS_DSN \
    session_cookie_domain=BOL_SESSION_COOKIE_DOMAIN \
    session_encrypt_key=BOL_SESSION_ENCRYPTION_KEY \
    session_validate_key=BOL_SESSION_VALIDATION_KEY \
    report_version_id06=BOL_REPORT_VERSION_ID06 \
    bulkimport_data_provider=BOL_BULKIMPORT_DATA_PROVIDER \
    report_swedish_data_provider=BOL_REPORT_SWEDISH_DATA_PROVIDER \
    report_foreign_data_provider=BOL_REPORT_FOREIGN_DATA_PROVIDER \
    tax_data_provider=bolfak.statusreports.noop.NoopProvider \
    creditsafe_username=BOL_CREDITSAFE_USER_ID \
    creditsafe_password=BOL_CREDITSAFE_PASSWORD \
    creditsafe_symmetric_secret_key=BOL_CREDITSAFE_SYMMETRIC_SECRET_KEY \
    creditsafe_ggs_wsdl_username=BOL_CREDITSAFE_GGS_WSDL_USERNAME \
    creditsafe_ggs_wsdl_password=BOL_CREDITSAFE_GGS_WSDL_PASSWORD \
    autoaccount_url=BOL_AUTOACCOUNT_URL \
    autoaccount_username=BOL_AUTOACCOUNT_USERNAME \
    autoaccount_password=BOL_AUTOACCOUNT_PASSWORD \
    autoaccount_request_package=BOL_AUTOACCOUNT_REQUEST_PACKAGE \
    autoaccount_use_testing_org_gov_org_id=BOL_AUTOACCOUNT_USE_TESTING_ORG_GOV_ORG_ID \
    autoaccount_testing_org_gov_org_id=BOL_AUTOACCOUNT_TESTING_ORG_GOV_ORG_ID \
    autoaccount_id06_gov_org_id=BOL_AUTOACCOUNT_ID06_GOV_ORG_ID \
    skatteverket_client_cert=/etc/bolfak/secrets/skatteverket_client_cert.pem \
    skatteverket_ca_cert=/etc/bolfak/secrets/skatteverket_ca_cert.pem \
    sendgrid_sender= \
    sendgrid_api_key= \
    worker_process_count=2 \
    qvarn_threadpool_size=20 \
    session_cookie_secure=yes \
    log_level=DEBUG \
    test_api=no \
    testdata_api=no \
    newrelic_license_key= \
    NEW_RELIC_CONFIG_FILE=/etc/bolfak/newrelic.ini \
    NEW_RELIC_ENVIRONMENT=dev \
    feature_flag_company_related_projects=yes \
    feature_flag_extended_report=no \
    service_key=bolagsfakta \
    content_config_name=id06-fs3.yaml \
    bol_permission=bolagsdeklaration_user \
    raportit_url=https://raportit.tilaajavastuu.fi/ \
    valvoja_url=https://devel-raportit.tilaajavastuu.fi/ \
    veronumero_url=https://devel-www.veronumero.fi/ \
    taito_url=https://taito.alpha.tilaajavastuu.fi/ \
    reseller_tool_url=https://reseller.alpha.tilaajavastuu.fi/ \
    terms_of_service_version=unknown \
    shared_cookie_domain=BOL_SHARED_COOKIE_DOMAIN \
    lima_base_url=https://lima-api.alpha.tilaajavastuu.fi \
    lima_version=v2 \
    lima_client_id=foobar \
    lima_client_secret=foobar \
    company_api_base_url=https://companyapi.alpha.tilaajavastuu.fi \
    company_api_version=v1 \
    company_api_client_id=foobar \
    company_api_client_secret=foobar \
    sp_framework_api_url=SP_FRAMEWORK_API_URL \
    sp_framework_api_verify_tls=SP_FRAMEWORK_API_VERIFY_TLS \
    i18n_variant=id06 \
    change_user_details_url=BOL_CHANGE_USER_DETAILS_URL \
    feature_flag_bda_client=yes \
    feature_flag_bda_company_list=no \
    feature_flag_bda_project_suppliers=no \
    feature_flag_cqpoc_client=no \
    feature_flag_cqpoc_get_companies=no \
    feature_flag_user_account_api=no \
    feature_flag_web_reports=no \
    feature_flag_web_reports_ee=no \
    feature_flag_web_reports_combined=no

RUN addgroup --gid 1000 bol && adduser --system --uid 1000 --gid 1000 --disabled-password bol
# /run.sh wants to edit some configuration on startup
RUN chown -R bol:bol /etc/bolfak/ /srv/bolfak/www/ /etc/uwsgi/apps-enabled/
# nginx starts up as user bol, not as root
RUN chown -R bol:bol /var/lib/nginx/
RUN chown -R bol:bol /etc/nginx/
USER 1000:1000

ENTRYPOINT ["/run.sh"]

EXPOSE 8080

CMD ["uwsgi", "--ini", "/etc/uwsgi/apps-enabled/bolagsfakta.ini"]
