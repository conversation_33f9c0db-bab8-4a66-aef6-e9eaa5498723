"""
Add person_id to user_accounts in BDA. This script is intended to be run once per environment
to copy the person_id from the user_account_api to the user_accounts in BDA.
"""

import argparse
import logging
import sys
import time

from requests import HTTPError, Response

from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import Storage, setup_storage
from bolfak.clients.user_account_api import setup_user_account_api_client
from bolfak.config import set_config


logger = logging.getLogger(__name__)


def retry_request(request, no_of_attempts=3, first_retry_interval=0.1) -> Response:
    for i in range(no_of_attempts):
        response = request()
        if response.status_code == 200:
            return response
        if i < no_of_attempts - 1:  # Don't sleep after last attempt
            time.sleep(first_retry_interval * (2**i))
    return response


def migrate_data(storage: Storage):
    bda_project_users_response = retry_request(
        lambda: storage.bda.get(
            '/project_users/query?q={"user_account_id__ne": null}', ignore_errors=True)
    )
    if bda_project_users_response.status_code != 200:
        logger.error(
            'Failed to get project users. Error %d %s',
            bda_project_users_response.status_code,
            bda_project_users_response.text,
        )
        sys.exit('Exiting')

    project_users = bda_project_users_response.json().get('resources', [])
    logger.info('Updating %d project users', len(project_users))
    i = 0
    for project_user in project_users:
        i += 1
        if i % 100 == 0:
            logger.info('%d/%d project users done', i, len(project_users))
        try:
            uaa_response = retry_request(
                lambda: storage.user_account_api.get_user_account(
                    project_user['user_account_id']
                )
            )
        except HTTPError as e:
            if e.response.status_code == 404:
                logger.warning(
                    'User account for %s not found (user account id was %s). Skipping.',
                    project_user['id'],
                    project_user['user_account_id'],
                )
                continue
        except Exception as e:
            logger.error(
                'Failed to get user account for user account id %s. Error %s',
                project_user['user_account_id'],
                e,
            )
            continue
        if uaa_response.status_code != 200:
            logger.warning(
                'Failed to get user account for %s. Error %d %s',
                project_user['user_account_id'],
                uaa_response.status_code,
                uaa_response.text,
            )
            continue

        user_account = uaa_response.json()
        put_response = retry_request(
            lambda: storage.bda.put(
                f'/project_users/{project_user["id"]}',
                json={'person_id': user_account['person_id']},
                ignore_errors=True,
            )
        )
        if put_response.status_code != 200:
            logger.error(
                'Failed to update person_id=%s for project user %s. Error %d %s',
                user_account['person_id'],
                project_user['id'],
                put_response.status_code,
                put_response.text,
            )

    missing_person_ids = retry_request(
        lambda: storage.bda.get('/project_users/query?q={"person_id__eq": null}')
    )
    if missing_person_ids.status_code != 200:
        logger.error(
            'Failed to get project users with missing person_id. Error %d %s',
            missing_person_ids.status_code,
            missing_person_ids.text,
        )
    for project_user in missing_person_ids.json().get('resources', []):
        logger.error(
            'Found project_user with no person_id. ID is %s', project_user['id']
        )


def main(args=None):
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        '-c',
        '--config',
        metavar='FILE',
        required=True,
        help='app config file for user account api and bda connection details',
    )
    args = parser.parse_args(args)

    conf = set_config(args.config)
    setup_azure_ai(conf, 'bol-add-person-id-to-project_users')

    qvarn = None
    core = None
    bda = setup_bda_client(conf)
    user_account_api = setup_user_account_api_client(conf)
    storage: Storage = setup_storage(
        qvarn=qvarn, bda=bda, core=core, user_account_api=user_account_api
    )

    logger.info('Adding person_id to project_users starting')
    migrate_data(storage)
    logger.info('Adding person_id to project_users finished')


if __name__ == '__main__':
    main()
