import os
import argparse
import pathlib
import yaml
import requests
import logging
from typing import List, Dict, Any

from bolfak.config import get_config, set_config
from bolfak.services.fixtures import get_fixture_path
from bolfak.clients.core import setup_core_sysapi_client, get_core_base_url

# TODO: Manage passwords in a better way
DEFAULT_PASSWORD = 'TestPass1'  # nosec

logger = logging.getLogger(__name__)


def load_fixture_users(filename: str) -> list[dict]:
    data = yaml.safe_load(pathlib.Path(filename).read_text())
    users = data['users']
    core_persons = []

    required_keys = ('uuid', 'userName', 'givenName', 'familyName', 'govOrgId')
    for user in users:
        if all(key in user for key in required_keys):
            core_persons.append({'uuid': user['uuid'],
                                 'email': user['userName'],
                                 'firstName': user['givenName'],
                                 'lastName': user['familyName'],
                                 'gov_org_id': user['govOrgId'],
                                 'user_role': user.get('userRole')})
        else:
            raise ValueError(f'{user} does not have required fields, {required_keys}')

    return core_persons


def create_person(core_person_dict: dict):
    """Create a core person"""
    data_to_post = core_person_dict.copy()
    data_to_post.pop('gov_org_id', None)
    data_to_post.pop('user_role', None)
    resp = requests.post(f'{get_core_base_url()}/test-api/person', json=data_to_post)
    if resp.status_code == 200 or resp.status_code == 201:
        uuid = core_person_dict['uuid']
        create_gluu_account(uuid, DEFAULT_PASSWORD)
        logger.info('%s successfully created', uuid)
    elif resp.status_code == 409:
        logger.warning('person with email %s already exists', core_person_dict['email'])
    else:
        logger.error('failed with status code %d, could not create person %s',
                     resp.status_code, core_person_dict['uuid'])


def create_organisation_person(core_org_person_dict: dict):
    """Create a core organisation person"""
    resp = requests.post(f'{get_core_base_url()}/test-api/organisation-person',
                         json=core_org_person_dict)
    if resp.status_code == 200 or resp.status_code == 201:
        uuid = core_org_person_dict['organisationUuid']
        logger.info('%s successfully created', uuid)
    elif resp.status_code == 409:
        logger.warning('organisation person with email %s already exists',
                       core_org_person_dict['professionalEmail'])
    else:
        logger.error('failed with status code %d, could not create organisation %s person %s',
                     resp.status_code, core_org_person_dict['organisationUuid'],
                     core_org_person_dict['uuid'])


def delete_person(core_person_uuid: str):
    """Delete a gluu account and a core person"""
    resp_delete = requests.delete(f'{get_core_base_url()}/test-api/person/' + core_person_uuid)
    if resp_delete.status_code == 200:
        logger.info('%s successfully deleted', core_person_uuid)
    elif resp_delete.status_code == 404:
        logger.info('%s does not exist, delete skipped', core_person_uuid)
    else:
        logger.warning('%s could not be deleted', core_person_uuid)


def create_gluu_account(core_person_uuid: str, gluu_password: str):
    """Create a gluu account from a core person uuid"""
    password_dict = {'uuid': core_person_uuid, 'password': gluu_password}
    resp_set_password = requests.post(
        f'{get_core_base_url()}/test-api/person/set-password', json=password_dict)
    if resp_set_password.status_code != 200:
        logger.warning('could not set password for person %s', core_person_uuid)


def get_person_email(core_sysapi_client, core_person_uuid: str) -> str | None:
    """Check if a core person exists and has an email"""
    try:
        resp_get_person = core_sysapi_client.get(f'/person/{core_person_uuid}')
    except requests.exceptions.HTTPError:
        logger.warning('person %s does not exist', core_person_uuid)
        return None

    if resp_get_person.status_code == 200:
        logger.info('%s exists', core_person_uuid)
        return resp_get_person.json()['email']

    return None


def find_company_by_gov_org_id(core_orgs_dict_list, user_gov_org_id):
    for company in core_orgs_dict_list:
        if company.get('govOrgId') == user_gov_org_id:
            return company
    return None


def sync_users(core_sysapi_client, core_person_dict_list: list, core_orgs_dict_list: list):
    """Check if user exists in gluu and that email is same as fixture data,
       else create new gluu account"""
    for core_person_dict in core_person_dict_list:
        person_email = get_person_email(core_sysapi_client, core_person_dict['uuid'])
        # If person does not exist or has different email than fixture, create new person
        if person_email != core_person_dict['email'] or not person_email:
            delete_person(core_person_dict['uuid'])
            create_person(core_person_dict)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--config',
        metavar='FILE.cfg',
        required=True,
        help='use FILE as configuration file for database connection details')
    parser.add_argument(
        '-f', '--fixture-file',
        metavar='FILE.yaml',
        default=get_fixture_path('robot.yaml'),
        help='load fixtures from this YAML file')
    args = parser.parse_args()

    set_config(args.config)
    config = get_config()

    section = 'core-system-api'
    base_url = config.get(section, 'base_url')

    os.environ['BOL_TEST_CORE_API_URL'] = base_url

    # TODO: crashes here with 403 if can't get access token for sysapi
    core_sysapi_client = setup_core_sysapi_client(config)

    fixture_file_path = args.fixture_file

    core_person_dict_list = load_fixture_users(fixture_file_path)
    core_org_person_dict_list: List[Dict[str, Any]] = []
    sync_users(core_sysapi_client, core_person_dict_list, core_org_person_dict_list)


if __name__ == '__main__':
    main()
