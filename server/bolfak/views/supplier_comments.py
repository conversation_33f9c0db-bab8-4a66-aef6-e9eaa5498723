import logging

from bottle import request
from bolfak.base import BaseBottle
from bolfak.decorators import org_context, qvarn_reason
from bolfak.views.common import (
    abort_forbidden,
    abort_not_found,
    get_resource_or_404,
    validate_json_request,
)
from bolfak.services import authentication, authorization, supplier_comments
from bolfak.views.models import (
    SupplierCommentsResponse,
    SupplierCommentCreatedResponse,
    SupplierCommentUpdatedResponse,
)
from bolfak.storage.supplier_comments import (
    get_supplier_comment,
)
from bolfak.forms.supplier_comments import (
    AddSupplierCommentForm,
    UpdateSupplierCommentForm,
    MarkCommentsAsReadForm,
)

logger = logging.getLogger(__name__)

app = BaseBottle()


@app.get("/<active_org_id>/supplier/<supplier_id>/comments")
@org_context
@qvarn_reason("getting supplier comments")
def get_supplier_comments(supplier_id) -> SupplierCommentsResponse:
    """Get comments about a supplier.

    Authorization:

    - Client, main contractor and supervisor can see all comments

    ---
    parameters:
      - name: supplier_id
        in: path
        description: supplier id to fetch comments for
        required: true
        schema:
          type: string
    responses:
      200:
        description: list of comments about the supplier
        content:
          application/json:
            schema:
              properties:
                comments:
                  type: array
                  description: list of comments, sorted by age (newest first)
                  items:
                    $ref: '#/components/schemas/supplier_comment'
            examples:
              empty result:
                description:
                  GET /api/12-12-12/supplier/12345678-1234-1234-1234-1234567890ab/comments
                value: |
                  {
                    "comments": []
                  }
              result with comments:
                description:
                  GET /api/12-12-12/supplier/12345678-1234-1234-1234-1234567890ab/comments
                value: |
                  {
                    "comments": [
                      {
                        "id": "45b1-b1971390ac129fa1ad619f964986d70e-86db97f6",
                        "comment": "This supplier is a nice person to work with",
                        "project_id": "AS-4444",
                        "supplier_id": "12345678-1234-1234-1234-1234567890ab",
                        "org_id": "12345678-1234-1234-1234-1234567890ab",
                        "author": "John Doe, Small Houses Inc.",
                        "updater": "John Doe, Small Houses Inc.",
                        "deleter": null,
                        "created_timestamp": "2022-01-01T00:00:00",
                        "updated_timestamp": "2022-01-01T00:00:00",
                        "deleted_timestamp": null,
                        "is_read": true,
                        "permissions": ["update", "delete"]
                      },
                      ...
                    ]
                  }
      404:
        $ref: '#/components/responses/not_found'
    tags:
      - supplier comments
    """
    profile = authentication.get_authenticated_user(request)
    supplier = get_resource_or_404(request.storage, "bol_suppliers", supplier_id)
    project_id = supplier["project_resource_id"]
    project = get_resource_or_404(request.storage, "projects", project_id)
    can_view_comments = authorization.can_view_supplier_comments(request, project)
    if not can_view_comments:
        abort_not_found()
    comments = supplier_comments.get_supplier_comments(
        request, supplier_id, profile["person_id"], project
    )
    return {"comments": comments}


@app.post("/<active_org_id>/supplier/<supplier_id>/add-comment")
@org_context
@qvarn_reason("adding supplier comment")
def add_supplier_comment(active_org_id, supplier_id) -> SupplierCommentCreatedResponse:
    """Add a comments about a supplier.

    Authorization:

    - Client, main contractor and supervisor can add comments

    ---
    parameters:
      - name: supplier_id
        in: path
        description: supplier id to add comment for
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              comment:
                type: string
    responses:
      200:
        description: created supplier comment
        content:
          application/json:
            ok:
              $ref: '#/components/schemas/ok'
            errors:
              $ref: '#/components/schemas/errors'
            comment:
              $ref: '#/components/schemas/supplier_comment'
            examples:
              success:
                description:
                  POST /api/12-12-12/supplier/12345678-1234-1234-1234-1234567890ab/comments/create
                value: |
                  {
                    "ok": true,
                    "entity": {
                      "id": "45b1-b1971390ac129fa1ad619f964986d70e-86db97f6",
                      "comment": "This supplier is a nice person to work with",
                      "project_id": "AS-4444",
                      "supplier_id": "12345678-1234-1234-1234-1234567890ab",
                      "org_id": "12345678-1234-1234-1234-1234567890ab",
                      "author": "John Doe, Small Houses Inc.",
                      "updater": "John Doe, Small Houses Inc.",
                      "deleter": null,
                      "created_timestamp": "2022-01-01T00:00:00",
                      "updated_timestamp": "2022-01-01T00:00:00",
                      "deleted_timestamp": null,
                      "is_read": true,
                      "permissions": ["update", "delete"]
                      },
                      ...
                    ]
                  }
      404:
        $ref: '#/components/responses/not_found'
      400:
        $ref: '#/components/responses/bad_request'
    tags:
      - supplier comments

    """
    data = validate_json_request(request, AddSupplierCommentForm)
    profile = authentication.get_authenticated_user(request)
    supplier = get_resource_or_404(request.storage, "bol_suppliers", supplier_id)
    project_id = supplier["project_resource_id"]
    project = get_resource_or_404(request.storage, "projects", project_id)
    can_add_comment = authorization.can_create_supplier_comments(request, project)
    if not can_add_comment:
        abort_not_found()
    comment = supplier_comments.create_supplier_comment(
        request,
        supplier,
        project,
        profile["person_id"],
        active_org_id,
        data["comment"],
    )
    return {"ok": True, "comment": comment}


@app.post("/<active_org_id>/supplier/<supplier_id>/comments/<comment_id>/update")
@org_context
@qvarn_reason("marking supplier comment as deleted")
def update_supplier_comment(
    active_org_id, supplier_id, comment_id
) -> SupplierCommentUpdatedResponse:
    """Update the text of a supplier comment.

    Authorization:

    - Client, main contractor and supervisor can see all comments
    - Users from the organisation that created the comment can update it
    - Client and main contractor can update comments in their projects

    ---
    parameters:
      - name: supplier_id
        in: path
        description: supplier the comment belongs to
        required: true
        schema:
          type: string
      - name: comment_id
        in: path
        description: comment id to update
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              comment:
                type: string
    responses:
      200:
        description: updated supplier comment
        content:
          application/json:
            $ref: '#/components/schemas/supplier_comment'
            examples:
              success:
                description:
                  POST /api/12-12-12/supplier/12345678-1234-1234-1234-1234567890ab/comments/\
12345678-1234-1234-1234-1234567890ab/mark-deleted
                value: |
                  {
                    "id": "45b1-b1971390ac129fa1ad619f964986d70e-86db97f6",
                    "comment": "This supplier is a nice person to work with",
                    "project_id": "AS-4444",
                    "supplier_id": "12345678-1234-1234-1234-1234567890ab",
                    "org_id": "12345678-1234-1234-1234-1234567890ab",
                    "author": "John Doe, Small Houses Inc.",
                    "updater": "John Doe, Small Houses Inc.",
                    "deleter": null,
                    "created_timestamp": "2022-01-01T00:00:00",
                    "updated_timestamp": "2022-01-01T00:00:00",
                    "deleted_timestamp": null,
                    "is_read": true,
                    "permissions": ["update", "delete"]
                  }
      400:
        $ref: '#/components/responses/bad_request'
      404:
        $ref: '#/components/responses/not_found'
      403:
        $ref: '#/components/responses/forbidden'
    tags:
      - supplier comments
    """
    data = validate_json_request(request, UpdateSupplierCommentForm)
    profile = authentication.get_authenticated_user(request)
    supplier = get_resource_or_404(request.storage, "bol_suppliers", supplier_id)
    project_id = supplier["project_resource_id"]
    project = get_resource_or_404(request.storage, "projects", project_id)
    can_view_comments = authorization.can_view_supplier_comments(request, project)
    if not can_view_comments:
        abort_not_found()
    comment = get_supplier_comment(request.storage, comment_id)
    can_update_supplier_comment = authorization.can_update_supplier_comment(
        request, project, comment
    )
    if not can_update_supplier_comment:
        abort_forbidden()
    comment_dto = supplier_comments.update_supplier_comment(
        request, comment, project, profile["person_id"], active_org_id, data["comment"]
    )
    return {
        "ok": True,
        "comment": comment_dto,
    }


@app.post("/<active_org_id>/supplier/<supplier_id>/comments/<comment_id>/mark-deleted")
@org_context
@qvarn_reason("marking supplier comment as deleted")
def mark_supplier_comment_deleted(
    active_org_id, supplier_id, comment_id
) -> SupplierCommentUpdatedResponse:
    """Mark a supplier comment as deleted.

    Authorization:

    - Client, main contractor and supervisor can see all comments
    - Users from the organisation that created the comment can update and delete it
    - Client and main contractor can update and delete comments in their projects

    ---
    parameters:
      - name: supplier_id
        in: path
        description: supplier the comment belongs to
        required: true
        schema:
          type: string
      - name: comment_id
        in: path
        description: comment id to mark as deleted
        required: true
        schema:
          type: string
    responses:
      200:
        description: updated supplier comment
        content:
          application/json:
            $ref: '#/components/schemas/supplier_comment'
            examples:
              success:
                description:
                  POST /api/12-12-12/supplier/12345678-1234-1234-1234-1234567890ab/comments/\
12345678-1234-1234-1234-1234567890ab/mark-deleted
                value: |
                  {
                    "ok": true,
                    "comment": {
                      "id": "45b1-b1971390ac129fa1ad619f964986d70e-86db97f6",
                      "comment": "This supplier is a nice person to work with",
                      "project_id": "AS-4444",
                      "supplier_id": "12345678-1234-1234-1234-1234567890ab",
                      "org_id": "12345678-1234-1234-1234-1234567890ab",
                      "author": "John Doe, Small Houses Inc.",
                      "updater": "John Doe, Small Houses Inc.",
                      "deleter": null,
                      "created_timestamp": "2022-01-01T00:00:00",
                      "updated_timestamp": "2022-01-01T00:00:00",
                      "deleted_timestamp": null,
                      "is_read": true,
                      "permissions": ["update", "delete"]
                    }
                  }
      404:
        $ref: '#/components/responses/not_found'
      403:
        $ref: '#/components/responses/forbidden'
    tags:
      - supplier comments
    """
    profile = authentication.get_authenticated_user(request)
    supplier = get_resource_or_404(request.storage, "bol_suppliers", supplier_id)
    project_id = supplier["project_resource_id"]
    project = get_resource_or_404(request.storage, "projects", project_id)
    can_view_comments = authorization.can_view_supplier_comments(request, project)
    if not can_view_comments:
        abort_not_found()
    comment = get_supplier_comment(request.storage, comment_id)
    can_delete_comment = authorization.can_delete_supplier_comment(
        request, project, comment
    )
    if not can_delete_comment:
        abort_forbidden()
    comment_dto = supplier_comments.mark_supplier_comment_as_deleted(
        request, comment, project, profile["person_id"], active_org_id
    )

    return {"ok": True, "comment": comment_dto}


@app.post("/<active_org_id>/supplier/<supplier_id>/comments/mark-read")
@org_context
@qvarn_reason("marking supplier comments as read")
def mark_supplier_comments_read(active_org_id, supplier_id):
    """Mark supplier comments as read for the person calling the endpoint.

    Authorization:

    - Client, main contractor and supervisor can mark comments as read

    ---
    parameters:
      - name: supplier_id
        in: path
        description: supplier the comments belong to
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              comment_ids:
                type: array
                items:
                  type: string
                description: list of comment IDs to mark as read
    responses:
      200:
        content:
          application/json:
            schema:
              type: object
              properties:
                ok:
                  type: boolean
                  example: true
                  description: true iff every comment was successfully marked read. If one \
or more failed, it's false.
            examples:
              success:
                description:
                  POST /api/12-12-12/supplier/12345678-1234-1234-1234-1234567890ab/comments\
/mark-read
                value: |
                  {
                    "ok": true
                  }
      400:
        $ref: '#/components/responses/bad_request'
      404:
        $ref: '#/components/responses/not_found'
    tags:
      - supplier comments
    """
    data = validate_json_request(request, MarkCommentsAsReadForm)
    profile = authentication.get_authenticated_user(request)
    supplier = get_resource_or_404(request.storage, "bol_suppliers", supplier_id)
    project_id = supplier["project_resource_id"]
    project = get_resource_or_404(request.storage, "projects", project_id)
    can_view_comments = authorization.can_view_supplier_comments(request, project)
    if not can_view_comments:
        abort_not_found()

    operation_succeeded = supplier_comments.mark_supplier_comments_as_read(
        request, data["comment_ids"], profile["person_id"]
    )

    return {"ok": operation_succeeded}
