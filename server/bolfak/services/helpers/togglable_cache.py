"""
A decorator that provides togglable caching functionality - useful for methods that only should be
cached when e.g. running a script and not when running the server.
"""

from typing import Callable, Optional, TypeVar, Hashable
from collections import OrderedDict, namedtuple
from threading import Lock

T = TypeVar("T")  # Return type of the decorated function

CacheInfo = namedtuple("CacheInfo", ["hits", "misses", "maxsize", "currsize"])


def skip_first(_first, *args, **kwargs):
    """
    Creates a cache key by forming a tuple from all parameters except the first. Useful for all
    functions whose first parameter is `storage` or `qvarn`

    Args:
        first: The first parameter to skip
        *args: Positional arguments
        **kwargs: Keyword arguments

    Returns:
        A tuple containing args and sorted kwargs as the cache key

    """
    return (args, tuple(sorted(kwargs.items())))


class togglable_cache:
    """
    An LRU cache that can be toggled off and on. Starts disabled, enable by calling
    `.cache_enabled()` e.g.:
    ```
    @togglable_cache
    def expensive_func(param1, param2):
        ...
    with expensive_func.cache_enabled():
        ...
    ```
    Also comes with methods to enable/disable the cache manually, and to get cache info:
    ```
    @togglable_cache
    def expensive_func(param1, param2):
        ...
    expensive_func.enable_cache()
    expensive_func.disable_cache()
    expensive_func.cache_info()
    ```
    """
    def __init__(self, func: Callable[..., T]):
        self._func: Callable[..., T] = func
        self._hits = 0
        self._misses = 0
        self._potentially_cached_func = func
        self._cache = OrderedDict[Hashable, T]()
        self._max_size: Optional[int] = None

    def __call__(self, *args, **kwargs):
        return self._potentially_cached_func(*args, **kwargs)

    def enable_cache(
        self,
        key_function: Optional[Callable[..., Hashable]] = None,
        cache_size: Optional[int] = 10000,
    ):
        """
        Enables the cache. Make sure to call disable_cache() when done, or the cache will remain
        enabled (which can be problematic for unit tests since it retains state between tests).
        Or, use the provided context manager `with func.cache_enabled(): ...`

        Args:
            `key_function` -- a function that, given the same parameters as the
            cached function, returns a cache key (i.e. something hashable). If none is provided,
            defaults to using all params as a tuple as the cache key.
            `cache_size` -- the maximum size of the cache, in number of entries. Defaults to
            10000 to reduce the risk of out of memory errors. If set to None, cache will grow
            without bound.

        """
        self._lock = Lock()
        self._max_size = cache_size
        self._key_function = (
            key_function
            if key_function is not None
            else lambda *args, **kwargs: (args, tuple(sorted(kwargs.items())))
        )

        def function_with_cache(*args, **kwargs):
            key = self._key_function(*args, **kwargs)
            with self._lock:
                if key in self._cache:
                    if self._max_size is not None:
                        self._cache.move_to_end(key)
                    self._hits += 1
                    return self._cache[key]
            self._misses += 1
            result = self._func(*args, **kwargs)
            with self._lock:
                self._cache[key] = result
                if self._max_size is not None:
                    self._cache.move_to_end(key)
                    if len(self._cache) > self._max_size:
                        self._cache.popitem(last=False)
            return result

        self._potentially_cached_func = function_with_cache

    def disable_cache(self):
        """Disables the cache and reverts to using the original function."""
        self._hits = 0
        self._misses = 0
        self._cache.clear()
        self._potentially_cached_func = self._func

    def cache_info(self):
        """Get some useful info about the cache.

        Returns:
            A tuple containing the number of hits, misses, max size and current size of the
            cache.

        """
        return CacheInfo(self._hits, self._misses, self._max_size, len(self._cache))

    def cache_enabled(
        self,
        key_function: Optional[Callable[..., Hashable]] = None,
        cache_size: Optional[int] = 10000,
    ):
        """
        Context manager to enable the cache for the duration of the context.
        Usage:
            with func.cache_enabled(<parameters>):
                ...
        Equivalent to:
            func.enable_cache(<parameters>)
            ...
            func.disable_cache()
        """
        cache = self

        class _CacheContextManager:
            def __enter__(self_):
                cache.enable_cache(key_function=key_function, cache_size=cache_size)
                return cache

            def __exit__(self_, exc_type, exc_val, exc_tb):
                cache.disable_cache()

        return _CacheContextManager()
