from typing import Literal
from bolfak.clients.qvarn import QvarnResultDict
from bolfak.storage.persons import get_persons
from bolfak.storage.company import get_orgs
from bolfak.storage import supplier_comments
from bolfak.models import Comment
from bolfak.views.models import SupplierCommentDTO
from bolfak.services import authorization


def create_supplier_comment(
    request,
    supplier: QvarnResultDict,
    project: dict,
    created_by_person_id: str,
    created_by_org_id: str,
    content: str,
) -> SupplierCommentDTO:
    comment = supplier_comments.create_supplier_comment(
        request.storage,
        project["id"],
        supplier["id"],
        supplier["supplier_org_id"],
        created_by_person_id,
        created_by_org_id,
        content,
    )
    return transform_comments_to_DTOs(request, [comment], project)[0]


def get_supplier_comments(
    request, supplier_id: str, person_id: str, project: dict
) -> list[SupplierCommentDTO]:
    comments = supplier_comments.get_supplier_comments(
        request.storage, supplier_id, reader_person_id=person_id
    )
    comment_dicts = transform_comments_to_DTOs(request, comments, project)
    comment_dicts.sort(key=lambda x: x["created_timestamp"], reverse=True)
    return comment_dicts


def mark_supplier_comment_as_deleted(
    request,
    comment: Comment,
    project: dict,
    deleted_by_person_id: str,
    deleted_by_org_id: str,
) -> SupplierCommentDTO:
    comment = supplier_comments.mark_supplier_comment_as_deleted(
        request.storage, comment.id, deleted_by_person_id, deleted_by_org_id
    )
    return transform_comments_to_DTOs(request, [comment], project)[0]


def update_supplier_comment(
    request,
    comment: Comment,
    project: dict,
    updated_by_person_id: str,
    updated_by_org_id: str,
    content: str,
) -> SupplierCommentDTO:
    updated_comment = supplier_comments.update_supplier_comment(
        request.storage,
        comment.id,
        updated_by_person_id,
        updated_by_org_id,
        content,
    )
    return transform_comments_to_DTOs(request, [updated_comment], project)[0]


def transform_comments_to_DTOs(
    request, comments: list[Comment], project: dict, should_get_permissions=True,
) -> list[SupplierCommentDTO]:
    person_ids = set(
        [c.created_by_person_id for c in comments]
        + [c.updated_by_person_id for c in comments if c.updated_by_person_id is not None]
        + [c.deleted_by_person_id for c in comments if c.deleted_by_person_id is not None]
    )
    org_ids = set(
        [c.created_by_org_id for c in comments]
        + [c.updated_by_org_id for c in comments if c.updated_by_org_id is not None]
        + [c.deleted_by_org_id for c in comments if c.deleted_by_org_id is not None]
    )
    persons = get_persons(request.storage, list(person_ids))
    orgs = get_orgs(request.storage, list(org_ids))
    orgs_map = {org["id"]: org for org in orgs}
    persons_map = {person["uuid"]: person for person in persons}
    return [
        {
            "id": comment.id,
            "comment": comment.comment if not comment.is_deleted else None,
            "project_id": comment.project_id,
            "supplier_id": comment.supplier_id,
            "org_id": comment.org_id,
            "author": _get_comment_org_and_person_name(comment, "created", orgs_map, persons_map),
            "updater": _get_comment_org_and_person_name(comment, "updated", orgs_map, persons_map),
            "deleter": _get_comment_org_and_person_name(comment, "deleted", orgs_map, persons_map),
            "created_timestamp": comment.created_timestamp.isoformat(timespec="seconds"),
            "updated_timestamp": comment.updated_timestamp.isoformat(timespec="seconds")
            if comment.updated_timestamp
            else None,
            "deleted_timestamp": comment.deleted_timestamp.isoformat(timespec="seconds")
            if comment.deleted_timestamp
            else None,
            "is_read": comment.is_read if comment.is_read is not None else True,
            "permissions": ([] if not should_get_permissions
                            else _get_comment_permissions(request, project, comment)),
        }
        for comment in comments
    ]


def _get_comment_org_and_person_name(
    comment: Comment,
    type: Literal["created", "updated", "deleted"],
    org_map,
    persons_map,
):
    if type == "created":
        org = org_map.get(comment.created_by_org_id)
        person = persons_map.get(comment.created_by_person_id, {})
    elif type == "updated":
        if not comment.is_updated:
            return None
        org = org_map.get(comment.updated_by_org_id)
        person = persons_map.get(comment.updated_by_person_id, {})
    elif type == "deleted":
        if not comment.is_deleted:
            return None
        org = org_map.get(comment.deleted_by_org_id)
        person = persons_map.get(comment.deleted_by_person_id, {})
    org_name = org["names"][0] if org else None
    person_name = person.get("full_name", None)

    if person_name and org_name:
        return f"{person_name}, {org_name}"
    if person_name:
        return person_name
    if org_name:
        return org_name
    return None


def mark_supplier_comments_as_read(
    request, comment_ids: list[str], reader_person_id: str
) -> bool:
    viewers = supplier_comments.mark_comments_as_read(
        request.storage, comment_ids=comment_ids, read_by_person_id=reader_person_id
    )
    # If not all viewers were created, consider the operation a failure
    return len(viewers) == len(comment_ids)


def _get_comment_permissions(
    request, project: QvarnResultDict, comment: Comment
) -> list[str]:
    permissions = []
    if authorization.can_update_supplier_comment(request, project, comment):
        permissions.append("update")
    if authorization.can_delete_supplier_comment(request, project, comment):
        permissions.append("delete")
    return permissions
