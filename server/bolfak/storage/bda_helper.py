from bolfak.config import MAX_ORG_IDS_PER_QUERY
from typing import Collection, Dict, Optional, Any
import json

from bolfak.clients.qvarn import QvarnResultDict


def get_multiple_entities_in_batches(
    storage,
    entity_name: str,
    query_ids: Collection[str],
    query_key: str,
    select: Optional[list[str]] = None,
    additional_filters: Optional[Dict[str, Any]] = None,
) -> list[QvarnResultDict]:
    if not query_ids:
        return []
    query_ids = list(set(query_ids))  # Remove duplicates
    query: dict[str, Any] = {}
    if additional_filters:
        query.update(additional_filters)

    # Batch requests as size of GET payload is limited
    batch_size = MAX_ORG_IDS_PER_QUERY
    all_entities = []
    futures = []
    for start in range(0, len(query_ids), batch_size):
        query[f"{query_key}__any"] = query_ids[start: start + batch_size]
        url = f"/{entity_name}/query?q={json.dumps(query)}"
        if select is not None:
            url += f"&show={json.dumps(select)}"
        futures.append(storage.bda.get_async(url))
    for future in futures:
        response = future.result()
        if not response.ok:
            raise RuntimeError(
                f"Failed to fetch suppliers: {response.status_code} {response.text}"
            )
        entities = response.json()["resources"]
        all_entities += entities
    return [QvarnResultDict(entity) for entity in all_entities]
