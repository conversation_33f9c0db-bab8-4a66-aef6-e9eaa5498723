from bolfak.services.helpers.togglable_cache import togglable_cache, skip_first


def test_togglable_cache_disabled_by_default():
    calls = []

    @togglable_cache
    def f(x):
        calls.append(x)
        return x * 2

    assert f(2) == 4
    assert f(2) == 4
    assert calls == [2, 2]  # called twice, no cache
    info = f.cache_info()
    assert info.hits == 0
    assert info.misses == 0
    assert info.currsize == 0


def test_togglable_cache_enable_and_hits():
    calls = []

    @togglable_cache
    def f(x):
        calls.append(x)
        return x + 1

    f.enable_cache()
    assert f(3) == 4
    assert f(3) == 4
    assert calls == [3]  # only called once
    info = f.cache_info()
    assert info.hits == 1
    assert info.misses == 1
    assert info.currsize == 1


def test_togglable_cache_isolation():
    calls_f = []
    calls_g = []

    @togglable_cache
    def f(x):
        calls_f.append(x)
        return x + 1

    @togglable_cache
    def g(x):
        calls_g.append(x)
        return x - 1

    f.enable_cache()
    g.enable_cache()
    assert f(3) == 4
    assert f(3) == 4
    assert calls_f == [3]
    assert g(3) == 2
    assert g(3) == 2
    assert g(3) == 2
    assert g(4) == 3
    assert calls_g == [3, 4]
    f_info = f.cache_info()
    assert f_info.hits == 1
    assert f_info.misses == 1
    assert f_info.currsize == 1
    g_info = g.cache_info()
    assert g_info.hits == 2
    assert g_info.misses == 2
    assert g_info.currsize == 2


def test_togglable_cache_default_size():
    calls = []

    @togglable_cache
    def f(x):
        calls.append(x)
        return x

    f.enable_cache()
    for i in range(15000):
        f(i)
    f(0)  # miss, evicted
    f(5001)  # hit
    info = f.cache_info()
    assert info.hits == 1
    assert info.misses == 15001
    assert info.currsize == 10000  # default max size is 10000
    assert calls == list(range(15000)) + [0]  # all calls except the hit


def test_togglable_cache_eviction():
    calls = []

    @togglable_cache
    def f(x):
        calls.append(x)
        return x

    f.enable_cache(cache_size=2)
    f(1)
    f(2)
    f(3)  # should evict 1
    f(2)  # hit
    f(1)  # miss, since evicted
    info = f.cache_info()
    assert info.hits == 1
    assert info.misses == 4
    assert info.currsize == 2
    assert calls.count(1) == 2


def test_togglable_cache_custom_key_function():
    calls = []

    @togglable_cache
    def f(a, b):
        calls.append((a, b))
        return a + b

    f.enable_cache(key_function=lambda a, b: a)
    assert f(1, 2) == 3
    assert f(1, 3) == 3  # same key, so should hit cache, value from first call
    assert calls == [(1, 2)]
    info = f.cache_info()
    assert info.hits == 1
    assert info.misses == 1
    assert info.currsize == 1


def test_togglable_cache_disable_cache_resets():
    calls = []

    @togglable_cache
    def f(x):
        calls.append(x)
        return x

    f.enable_cache()
    f(5)
    f(5)
    f.disable_cache()
    assert f(5) == 5
    assert calls.count(5) == 2  # called once with cache, once after disabling
    info = f.cache_info()
    assert info.hits == 0
    assert info.misses == 0
    assert info.currsize == 0


def test_skip_first_key_usage():
    calls = []

    @togglable_cache
    def f(storage, x, y=0):
        calls.append((storage, x, y))
        return x + y

    f.enable_cache(key_function=skip_first)
    assert f("s", 1, y=2) == 3
    assert f("s", 1, y=2) == 3
    assert calls == [("s", 1, 2)]
    info = f.cache_info()
    assert info.hits == 1
    assert info.misses == 1
    assert info.currsize == 1


def test_skip_first_key_respects_typing():
    assert skip_first(0) == skip_first(2)
    assert skip_first(0, "2") != skip_first(0, 2)
    assert skip_first(0, "2", "3", x="4") != skip_first(0, 2, 3, x=4)


def test_skip_first_key_handles_none():
    assert skip_first(0, None) != skip_first(0)
    assert skip_first(0, x=None) != skip_first(0)
    assert skip_first(0, x=None) != skip_first(0, None)
    assert skip_first(0, x=None) != skip_first(0, x="None")


def test_context_handler():
    calls = []

    @togglable_cache
    def f(x):
        calls.append(x)
        return x

    with f.cache_enabled():
        assert f(1) == 1
        assert f(1) == 1
    assert calls == [1]
    f(1)  # no cache, should call again
    assert calls == [1, 1]
    info = f.cache_info()
    assert info.hits == 0
    assert info.misses == 0
    assert info.currsize == 0


def test_does_not_cache_errors():
    calls = []

    @togglable_cache
    def f(x):
        calls.append(x)
        1 / x  # creates a runtime error for 0
        return x

    with f.cache_enabled():
        try:
            f(0)
        except ZeroDivisionError:
            pass
        else:
            assert False, "Should have raised"
        f(1)
        f(1)
        try:
            f(0)
        except ZeroDivisionError:
            pass
        else:
            assert False, "Should have raised"
        assert calls == [0, 1, 0]
        info = f.cache_info()
        assert info.hits == 1
        assert info.misses == 3
        assert info.currsize == 1
