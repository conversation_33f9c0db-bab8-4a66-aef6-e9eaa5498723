from bolfak.services.companies import get_project_comments_batch
from bolfak.models import Comment


def test_get_project_comments_batch_success(storage, mocker):
    """Test service layer batch function with successful API calls."""
    project_ids = ["project-1", "project-2", "project-3"]
    org_id = "test-org-id"

    # Mock the storage layer function
    mock_fetch = mocker.patch('bolfak.services.companies.fetch_project_comments_batch')
    mock_fetch.return_value = {
        'resource_type': 'project_supplier_comments_batch',
        'resources': {
            'project-1': [
                {
                    'id': 'comment-1',
                    'comment': 'Comment 1',
                    'project_id': 'project-1',
                    'org_id': org_id,
                    'supplier_id': 'supplier-1',
                    'created_by_org_id': org_id,
                    'created_by_person_id': 'person-1',
                    'created_timestamp': '2023-01-01T00:00:00Z',
                    'is_deleted': False,
                    'deleted_by_org_id': None,
                    'deleted_by_person_id': None,
                    'deleted_timestamp': None,
                    'is_updated': False,
                    'updated_by_org_id': None,
                    'updated_by_person_id': None,
                    'updated_timestamp': None,
                    'modified_timestamp': None,
                    'is_read': None
                }
            ],
            'project-2': [
                {
                    'id': 'comment-2',
                    'comment': 'Comment 2',
                    'project_id': 'project-2',
                    'org_id': org_id,
                    'supplier_id': 'supplier-2',
                    'created_by_org_id': org_id,
                    'created_by_person_id': 'person-2',
                    'created_timestamp': '2023-01-02T00:00:00Z',
                    'is_deleted': False,
                    'deleted_by_org_id': None,
                    'deleted_by_person_id': None,
                    'deleted_timestamp': None,
                    'is_updated': False,
                    'updated_by_org_id': None,
                    'updated_by_person_id': None,
                    'updated_timestamp': None,
                    'modified_timestamp': None,
                    'is_read': None
                }
            ],
            'project-3': []
        }
    }

    # Test the service function
    result = get_project_comments_batch(storage, project_ids, org_id)

    # Verify storage function was called correctly
    mock_fetch.assert_called_once_with(storage, project_ids, org_id)

    # Verify results are converted to Comment objects
    assert len(result) == 3
    assert 'project-1' in result
    assert 'project-2' in result
    assert 'project-3' in result

    # Check project-1 has 1 Comment object
    assert len(result['project-1']) == 1
    assert isinstance(result['project-1'][0], Comment)
    assert result['project-1'][0].id == 'comment-1'
    assert result['project-1'][0].comment == 'Comment 1'

    # Check project-2 has 1 Comment object
    assert len(result['project-2']) == 1
    assert isinstance(result['project-2'][0], Comment)
    assert result['project-2'][0].id == 'comment-2'

    # Check project-3 has no comments
    assert len(result['project-3']) == 0


def test_get_project_comments_batch_empty_input(storage):
    """Test service layer function with empty project list."""
    result = get_project_comments_batch(storage, [], "test-org-id")
    assert result == {}


def test_get_project_comments_batch_error_handling(storage, mocker):
    """Test service layer function handles storage errors gracefully."""
    project_ids = ["project-1", "project-2"]
    org_id = "test-org-id"

    # Mock the storage layer function to raise an exception
    mock_fetch = mocker.patch('bolfak.services.companies.fetch_project_comments_batch')
    mock_fetch.side_effect = Exception("BDA API error")

    # Mock logger to verify error logging
    mock_logger = mocker.patch('bolfak.services.companies.logger')

    # Test the service function - should handle error gracefully
    result = get_project_comments_batch(storage, project_ids, org_id)

    # Should return empty results for all projects
    assert len(result) == 2
    assert result["project-1"] == []
    assert result["project-2"] == []

    # Should log the error
    mock_logger.warning.assert_called_once()
    assert "Failed to fetch comments for batch" in mock_logger.warning.call_args[0][0]


def test_get_project_comments_batch_large_input(storage, mocker):
    """Test service layer function handles large project lists by batching."""
    # Create 150 project IDs to test batching (limit is 100)
    project_ids = [f"project-{i}" for i in range(150)]
    org_id = "test-org-id"

    # Mock the storage layer function to be called twice
    mock_fetch = mocker.patch('bolfak.services.companies.fetch_project_comments_batch')

    # First batch response (projects 0-99)
    first_batch_response = {
        'resource_type': 'project_supplier_comments_batch',
        'resources': {pid: [] for pid in project_ids[:100]}
    }

    # Second batch response (projects 100-149)
    second_batch_response = {
        'resource_type': 'project_supplier_comments_batch',
        'resources': {pid: [] for pid in project_ids[100:]}
    }

    mock_fetch.side_effect = [first_batch_response, second_batch_response]

    # Test the service function
    result = get_project_comments_batch(storage, project_ids, org_id)

    # Should make 2 calls to storage layer (batches of 100)
    assert mock_fetch.call_count == 2

    # Verify first batch call
    first_call = mock_fetch.call_args_list[0]
    assert first_call[0][1] == project_ids[:100]  # First 100 project IDs
    assert first_call[0][2] == org_id

    # Verify second batch call
    second_call = mock_fetch.call_args_list[1]
    assert second_call[0][1] == project_ids[100:]  # Remaining 50 project IDs
    assert second_call[0][2] == org_id

    # Should return results for all 150 projects
    assert len(result) == 150
    for project_id in project_ids:
        assert project_id in result
        assert result[project_id] == []


def test_get_project_comments_batch_partial_failure(storage, mocker):
    """Test service layer function handles partial batch failures."""
    # Create project IDs that will be split into 2 batches
    project_ids = [f"project-{i}" for i in range(150)]
    org_id = "test-org-id"

    # Mock the storage layer function - first succeeds, second fails
    mock_fetch = mocker.patch('bolfak.services.companies.fetch_project_comments_batch')

    # First batch succeeds
    first_batch_response = {
        'resource_type': 'project_supplier_comments_batch',
        'resources': {
            pid: [{
                'id': f'comment-{pid}',
                'comment': f'Comment for {pid}',
                'project_id': pid,
                'org_id': org_id,
                'supplier_id': f'supplier-{pid}',
                'created_by_org_id': org_id,
                'created_by_person_id': 'person-1',
                'created_timestamp': '2023-01-01T00:00:00Z',
                'is_deleted': False,
                'deleted_by_org_id': None,
                'deleted_by_person_id': None,
                'deleted_timestamp': None,
                'is_updated': False,
                'updated_by_org_id': None,
                'updated_by_person_id': None,
                'updated_timestamp': None,
                'modified_timestamp': None,
                'is_read': None
            }] for pid in project_ids[:100]
        }
    }

    # Second batch fails
    mock_fetch.side_effect = [first_batch_response, Exception("Second batch failed")]

    # Mock logger
    mock_logger = mocker.patch('bolfak.services.companies.logger')

    # Test the service function
    result = get_project_comments_batch(storage, project_ids, org_id)

    # Should make 2 calls to storage layer
    assert mock_fetch.call_count == 2

    # Should return results for all 150 projects
    assert len(result) == 150

    # First 100 projects should have comments (from successful batch)
    for i in range(100):
        project_id = f"project-{i}"
        assert len(result[project_id]) == 1
        assert isinstance(result[project_id][0], Comment)
        assert result[project_id][0].id == f'comment-{project_id}'

    # Last 50 projects should have empty results (from failed batch)
    for i in range(100, 150):
        project_id = f"project-{i}"
        assert result[project_id] == []

    # Should log the error for the failed batch
    mock_logger.warning.assert_called_once()
    assert "Failed to fetch comments for batch" in mock_logger.warning.call_args[0][0]
