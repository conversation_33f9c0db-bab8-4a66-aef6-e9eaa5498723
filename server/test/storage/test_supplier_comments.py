from bolfak.fixtures import factories
from bolfak.storage import supplier_comments


def test_create_and_get_supplier_comment(storage):
    project = factories.create_default_project(storage)
    suppliers_tree = factories.create_suppliers_tree(storage, project,
                                                     [factories.supplier('Supplier1')])
    supplier_id = suppliers_tree[0].id
    person = factories.create_person(storage)
    comment_obj = supplier_comments.create_supplier_comment(
        storage,
        project_id=project["id"],
        supplier_id=supplier_id,
        org_id="some-org-id",
        commentor_person_id=person["id"],
        commentor_org_id="my-org-id",
        comment_text="a test comment",
    )
    assert comment_obj.comment == 'a test comment'
    assert comment_obj.project_id == project['id']
    [comment] = supplier_comments.get_supplier_comments(
        storage, supplier_id, reader_person_id=person["id"]
    )

    assert comment.id == comment_obj.id
    assert comment.project_id == project['id']
    assert comment.supplier_id == supplier_id
    assert comment.org_id == 'some-org-id'
    assert comment.comment == 'a test comment'
    assert comment.created_by_org_id == 'my-org-id'
    assert comment.created_by_person_id == person['id']
    assert comment.created_timestamp is not None
    assert comment.is_deleted is False
    assert comment.deleted_by_org_id is None
    assert comment.deleted_by_person_id is None
    assert comment.deleted_timestamp is None
    assert comment.is_updated is False
    assert comment.updated_by_org_id is None
    assert comment.updated_by_person_id is None
    assert comment.updated_timestamp is None
    assert comment.modified_timestamp is None
    assert comment.is_read is True


def test_get_supplier_comments(storage):
    project = factories.create_default_project(storage)
    suppliers_tree = factories.create_suppliers_tree(
        storage, project, [factories.supplier("Supplier2")]
    )
    supplier_id = suppliers_tree[0].id
    comment_obj = supplier_comments.create_supplier_comment(
        storage,
        project_id=project["id"],
        supplier_id=supplier_id,
        org_id="some-org-id",
        commentor_person_id="person-id-1",
        commentor_org_id="my-org-id",
        comment_text="Read test comment",
    )
    [comment] = supplier_comments.get_supplier_comments(
        storage, supplier_id, reader_person_id="person-id-1"
    )
    assert comment.id == comment_obj.id
    assert comment.project_id == project["id"]
    assert comment.supplier_id == supplier_id
    assert comment.org_id == "some-org-id"
    assert comment.comment == "Read test comment"
    assert comment.created_by_org_id == "my-org-id"
    assert comment.created_by_person_id == "person-id-1"
    assert comment.created_timestamp is not None
    assert comment.is_deleted is False
    assert comment.deleted_by_org_id is None
    assert comment.deleted_by_person_id is None
    assert comment.deleted_timestamp is None
    assert comment.is_updated is False
    assert comment.updated_by_org_id is None
    assert comment.updated_by_person_id is None
    assert comment.updated_timestamp is None
    assert comment.modified_timestamp is None
    assert comment.is_read is True


def test_mark_comment_as_read(storage):
    project = factories.create_default_project(storage)
    org = factories.create_org(storage)
    suppliers_tree = factories.create_suppliers_tree(storage, project,
                                                     [factories.supplier('Supplier2')])
    supplier_id = suppliers_tree[0].id
    comment_obj = supplier_comments.create_supplier_comment(
        storage,
        project_id=project["id"],
        supplier_id=supplier_id,
        org_id=org["id"],
        commentor_person_id="person-id-1",
        commentor_org_id=org["id"],
        comment_text="Read test comment",
    )
    viewer = supplier_comments.mark_comment_as_read(
        storage, comment_obj.id, "person-id-2"
    )
    [read_comment] = supplier_comments.get_supplier_comments_for_project(
        storage, project["id"], reader_person_id="person-id-2"
    )
    [unread_comment] = supplier_comments.get_supplier_comments_for_project(
        storage, project["id"], reader_person_id="person-id-3"
    )
    assert viewer.comment_id == comment_obj.id
    assert viewer.read_by_person_id == 'person-id-2'
    assert read_comment.id == comment_obj.id
    assert read_comment.is_read is True
    assert unread_comment.id == comment_obj.id
    assert unread_comment.is_read is False


def test_get_project_comments_batch(storage, mocker):
    """Test the batch project comments functionality with mocked BDA response."""
    project_id1 = "project-1"
    project_id2 = "project-2"
    project_id3 = "project-3"
    org_id = "test-org-id"

    # Mock the BDA response for the batch endpoint
    mock_response = mocker.MagicMock()
    mock_response.ok = True
    mock_response.json.return_value = {
        "resource_type": "project_supplier_comments_batch",
        "resources": {
            project_id1: [
                {
                    "id": "comment-1",
                    "project_id": project_id1,
                    "supplier_id": "supplier-1",
                    "org_id": org_id,
                    "comment": "Comment for project 1",
                    "created_by_person_id": "person-1",
                    "created_by_org_id": org_id,
                    "created_timestamp": "2023-01-01T10:00:00Z",
                    "is_deleted": False,
                    "deleted_by_org_id": None,
                    "deleted_by_person_id": None,
                    "deleted_timestamp": None,
                    "is_updated": False,
                    "updated_by_org_id": None,
                    "updated_by_person_id": None,
                    "updated_timestamp": None,
                    "modified_timestamp": None,
                    "is_read": True,
                }
            ],
            project_id2: [
                {
                    "id": "comment-2",
                    "project_id": project_id2,
                    "supplier_id": "supplier-2",
                    "org_id": org_id,
                    "comment": "Comment for project 2",
                    "created_by_person_id": "person-2",
                    "created_by_org_id": org_id,
                    "created_timestamp": "2023-01-01T11:00:00Z",
                    "is_deleted": False,
                    "deleted_by_org_id": None,
                    "deleted_by_person_id": None,
                    "deleted_timestamp": None,
                    "is_updated": False,
                    "updated_by_org_id": None,
                    "updated_by_person_id": None,
                    "updated_timestamp": None,
                    "modified_timestamp": None,
                    "is_read": True,
                }
            ],
            project_id3: []  # No comments for project 3
        },
        "project_ids": [project_id1, project_id2, project_id3]
    }

    # Mock the BDA post method
    storage.bda.post = mocker.MagicMock(return_value=mock_response)

    # Test batch retrieval
    project_ids = [project_id1, project_id2, project_id3]
    response_data = supplier_comments.fetch_project_comments_batch(
        storage, project_ids, org_id
    )

    # Verify response structure
    assert response_data['resource_type'] == 'project_supplier_comments_batch'
    assert 'resources' in response_data

    comments_by_project = response_data['resources']

    # Verify the BDA call was made correctly
    storage.bda.post.assert_called_once_with('/projects/comments/batch', json={
        'project_ids': project_ids,
        'org_id': org_id
    })

    # Verify results
    assert len(comments_by_project) == 3

    # Project 1 should have 1 comment (raw data)
    assert len(comments_by_project[project_id1]) == 1
    assert comments_by_project[project_id1][0]['id'] == "comment-1"
    assert comments_by_project[project_id1][0]['comment'] == "Comment for project 1"
    assert comments_by_project[project_id1][0]['project_id'] == project_id1

    # Project 2 should have 1 comment (raw data)
    assert len(comments_by_project[project_id2]) == 1
    assert comments_by_project[project_id2][0]['id'] == "comment-2"
    assert comments_by_project[project_id2][0]['comment'] == "Comment for project 2"
    assert comments_by_project[project_id2][0]['project_id'] == project_id2

    # Project 3 should have no comments
    assert len(comments_by_project[project_id3]) == 0


def test_fetch_project_comments_batch_empty_input(storage):
    """Test batch function with empty project list."""
    result = supplier_comments.fetch_project_comments_batch(storage, [], "test-org-id")
    assert result == {'resources': {}}


def test_fetch_project_comments_batch_bda_error(storage, mocker):
    """Test batch function raises exception on BDA errors."""
    project_ids = ["project-1", "project-2"]
    org_id = "test-org-id"

    # Mock BDA response with error
    mock_response = mocker.MagicMock()
    mock_response.raise_for_status.side_effect = Exception("BDA API error")
    storage.bda.post = mocker.MagicMock(return_value=mock_response)

    # Test batch retrieval with error - should raise exception
    import pytest
    with pytest.raises(Exception, match="BDA API error"):
        supplier_comments.fetch_project_comments_batch(
            storage, project_ids, org_id
        )


def test_fetch_project_comments_batch_invalid_response(storage, mocker):
    """Test batch function raises exception on invalid response structure."""
    project_ids = ["project-1", "project-2"]
    org_id = "test-org-id"

    # Mock BDA response with invalid structure
    mock_response = mocker.MagicMock()
    mock_response.raise_for_status.return_value = None  # No HTTP error
    mock_response.json.return_value = {
        "invalid_structure": True  # Missing resource_type and resources
    }
    storage.bda.post = mocker.MagicMock(return_value=mock_response)

    # Test batch retrieval with invalid response - should raise exception
    import pytest
    with pytest.raises(ValueError, match="Invalid response structure"):
        supplier_comments.fetch_project_comments_batch(
            storage, project_ids, org_id
        )
