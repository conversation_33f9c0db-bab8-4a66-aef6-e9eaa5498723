import pytest
from unittest.mock import Mock, patch

from bolfak.models import (
    LINKED,
    MAIN_CONTRACTOR_ROLE,
    SUPERVISOR_ROLE,
    SUPPLIER_ROLE,
    VISITOR,
)
from bolfak.storage.qvarn.suppliers import (
    get_projects_suppliers,
    get_orgs_suppliers,
)
from bolfak.fixtures import factories


@pytest.fixture
def integration_data(storage):
    # Create organizations
    org1 = factories.create_org(storage, name="Org 1")
    org2 = factories.create_org(storage, name="Org 2")
    org3 = factories.create_org(storage, name="Org 3")
    org4 = factories.create_org(storage, name="Org 4")
    org5 = factories.create_org(storage, name="Org 5")

    # Create projects
    project1 = factories.get_or_create_project(storage, "Test Project 1", org=org1)
    project2 = factories.get_or_create_project(storage, "Test Project 2", org=org2)

    # Create suppliers for project1
    suppliers_tree1 = [
        factories.supplier(
            org2, supplier_role=MAIN_CONTRACTOR_ROLE, supplier_type=LINKED
        ),
        factories.supplier(org3, supplier_role=SUPERVISOR_ROLE, supplier_type=LINKED),
        factories.supplier(org4, supplier_role=SUPPLIER_ROLE, supplier_type=LINKED),
        factories.supplier(org5, supplier_role=SUPPLIER_ROLE, supplier_type=VISITOR),
    ]
    suppliers1 = factories.create_suppliers_tree(storage, project1, suppliers_tree1)

    # Create suppliers for project2
    suppliers_tree2 = [
        factories.supplier(org1, supplier_role=SUPPLIER_ROLE, supplier_type=LINKED),
        factories.supplier(
            org3, supplier_role=MAIN_CONTRACTOR_ROLE, supplier_type=LINKED
        ),
    ]
    suppliers2 = factories.create_suppliers_tree(storage, project2, suppliers_tree2)

    data = {
        "projects": [project1, project2],
        "orgs": [org1, org2, org3, org4, org5],
        "suppliers": suppliers1 + suppliers2,
    }

    yield data

    # Cleanup after all tests
    for project in data["projects"]:
        storage.bda.delete(f"/projects/{project['id']}")


def create_mock_storage_with_response(
    resources=None, ok=True, status_code=200, error_text=None
):
    """Helper function to create a mock storage with configured response."""
    mock_storage = Mock()
    mock_future = Mock()
    mock_future.result.return_value = Mock(ok=ok, status_code=status_code)
    if ok:
        mock_future.result.return_value.json.return_value = {
            "resources": resources or []
        }
    else:
        mock_future.result.return_value.text = error_text
    mock_storage.bda.get_async.return_value = mock_future
    return mock_storage


def create_mock_storage_with_batched_responses(responses):
    """Helper function to create a mock storage with multiple batched responses."""
    mock_storage = Mock()
    mock_futures = []
    for response in responses:
        mock_future = Mock()
        mock_future.result.return_value = Mock(ok=True)
        mock_future.result.return_value.json.return_value = {"resources": response}
        mock_futures.append(mock_future)
    mock_storage.bda.get_async.side_effect = mock_futures
    return mock_storage


def supplier_test_functions_parametrization():
    """Decorator to parametrize tests for both supplier functions."""
    return pytest.mark.parametrize(
        "get_suppliers_fn,id_field",
        [
            (get_projects_suppliers, "project_resource_id"),
            (get_orgs_suppliers, "supplier_org_id"),
        ],
    )


@supplier_test_functions_parametrization()
def test_suppliers_empty_ids(get_suppliers_fn, id_field):
    """Test that empty IDs returns empty list for both supplier functions."""
    mock_storage = create_mock_storage_with_response([{"id": "supplier1"}])
    result = get_suppliers_fn(mock_storage, [])
    assert result == []
    assert not mock_storage.bda.get_async.called


@supplier_test_functions_parametrization()
def test_suppliers_single_id(get_suppliers_fn, id_field):
    """Test with a single ID for both supplier functions."""
    mock_storage = create_mock_storage_with_response([{"id": "supplier1"}])
    result = get_suppliers_fn(mock_storage, ["id1"])

    assert result == [{"id": "supplier1"}]
    mock_storage.bda.get_async.assert_called_once()
    call_args = mock_storage.bda.get_async.call_args[0][0]
    assert f"{id_field}__any" in call_args


@supplier_test_functions_parametrization()
@patch("bolfak.storage.bda_helper.MAX_ORG_IDS_PER_QUERY", 2)
def test_suppliers_batching(get_suppliers_fn, id_field):
    """Test batching behavior for both supplier functions."""
    responses = [[{"id": "supplier1"}], [{"id": "supplier2"}]]
    mock_storage = create_mock_storage_with_batched_responses(responses)

    result = get_suppliers_fn(mock_storage, ["id1", "id2", "id3"])
    assert result == [{"id": "supplier1"}, {"id": "supplier2"}]
    assert mock_storage.bda.get_async.call_count == 2


@supplier_test_functions_parametrization()
def test_suppliers_with_select(get_suppliers_fn, id_field):
    """Test that the select parameter works for both supplier functions."""
    mock_storage = create_mock_storage_with_response([])
    select_fields = ["id", "supplier_org_id", id_field]

    get_suppliers_fn(mock_storage, ["id1"], select=select_fields)

    call_args = mock_storage.bda.get_async.call_args[0][0]
    assert all(f'"{field}"' in call_args for field in select_fields)


@supplier_test_functions_parametrization()
def test_suppliers_with_filters(get_suppliers_fn, id_field):
    """Test filtering by supplier roles and types for both functions."""
    mock_storage = create_mock_storage_with_response([])

    get_suppliers_fn(
        mock_storage,
        ["id1"],
        supplier_roles=["main_contractor", "supervisor"],
        supplier_types=["linked", "unlinked"],
    )

    call_args = mock_storage.bda.get_async.call_args[0][0]
    assert "supplier_role__any" in call_args
    assert "supplier_type__any" in call_args


@supplier_test_functions_parametrization()
def test_suppliers_error_handling(get_suppliers_fn, id_field):
    """Test that HTTP errors are properly handled for both functions."""
    mock_storage = create_mock_storage_with_response(
        ok=False, status_code=500, error_text="Internal Server Error"
    )

    with pytest.raises(RuntimeError) as exc:
        get_suppliers_fn(mock_storage, ["id1"])
    assert "Failed to fetch suppliers: 500 Internal Server Error" in str(exc.value)


# Integration tests

def test_get_projects_suppliers_with_roles(storage, integration_data):
    """Test get_projects_suppliers with different role filters."""
    project1_id = integration_data["projects"][0]["id"]

    # Test main contractor filter
    suppliers = get_projects_suppliers(
        storage, [project1_id], supplier_roles=[MAIN_CONTRACTOR_ROLE]
    )
    assert len(suppliers) == 1
    assert suppliers[0]["supplier_role"] == MAIN_CONTRACTOR_ROLE

    # Test multiple roles
    suppliers = get_projects_suppliers(
        storage, [project1_id], supplier_roles=[MAIN_CONTRACTOR_ROLE, SUPERVISOR_ROLE]
    )
    assert len(suppliers) == 2
    roles = {s["supplier_role"] for s in suppliers}
    assert roles == {MAIN_CONTRACTOR_ROLE, SUPERVISOR_ROLE}


def test_get_projects_suppliers_with_types(storage, integration_data):
    """Test get_projects_suppliers with different type filters."""
    project1_id = integration_data["projects"][0]["id"]

    # Test linked suppliers
    suppliers = get_projects_suppliers(storage, [project1_id], supplier_types=[LINKED])
    assert all(s["supplier_type"] == LINKED for s in suppliers)

    # Test visitor suppliers
    suppliers = get_projects_suppliers(storage, [project1_id], supplier_types=[VISITOR])
    assert len(suppliers) == 1
    assert suppliers[0]["supplier_type"] == VISITOR


def test_get_orgs_suppliers_with_filters(storage, integration_data):
    """Test get_orgs_suppliers with different filters."""
    org1, org2, org3 = integration_data["orgs"][:3]

    # Test org3 which is both supervisor and main contractor
    suppliers = get_orgs_suppliers(
        storage, [org3["id"]], supplier_roles=[SUPERVISOR_ROLE], supplier_types=[LINKED]
    )
    assert len(suppliers) == 1
    assert suppliers[0]["supplier_role"] == SUPERVISOR_ROLE

    # Test with multiple org IDs
    suppliers = get_orgs_suppliers(
        storage,
        [org1["id"], org2["id"]],
        supplier_roles=[SUPPLIER_ROLE, MAIN_CONTRACTOR_ROLE],
    )
    assert len(suppliers) == 2
    roles = {s["supplier_role"] for s in suppliers}
    assert SUPPLIER_ROLE in roles
    assert MAIN_CONTRACTOR_ROLE in roles


def test_suppliers_with_field_selection(storage, integration_data):
    """Test both supplier functions with field selection."""
    project1_id = integration_data["projects"][0]["id"]
    org3_id = integration_data["orgs"][2]["id"]

    select_fields = ["id", "supplier_org_id", "supplier_role"]

    # Test projects suppliers
    suppliers = get_projects_suppliers(storage, [project1_id], select=select_fields)
    assert len(suppliers) > 0
    assert all(set(s.keys()) == set(select_fields) for s in suppliers)

    # Test orgs suppliers
    suppliers = get_orgs_suppliers(storage, [org3_id], select=select_fields)
    assert len(suppliers) > 0
    assert all(set(s.keys()) == set(select_fields) for s in suppliers)


@pytest.mark.parametrize("batch_size", [2, 3])
def test_get_orgs_suppliers_batching(storage, integration_data, batch_size):
    """Test batching behavior with different batch sizes using real storage."""
    with patch("bolfak.storage.bda_helper.MAX_ORG_IDS_PER_QUERY", batch_size):
        # Create a spy for the BDA get_async method
        get_async_spy = Mock(wraps=storage.bda.get_async)
        storage.bda.get_async = get_async_spy

        org_ids = {org["id"] for org in integration_data["orgs"]}
        suppliers = get_orgs_suppliers(storage, org_ids)

        # Verify that the endpoint was called multiple times
        expected_calls = len(org_ids) // batch_size + (
            1 if len(org_ids) % batch_size else 0
        )
        assert get_async_spy.call_count == expected_calls

        # Verify we got all suppliers
        org_ids_found = {s["supplier_org_id"] for s in suppliers}
        assert org_ids_found == set(org_ids)


@pytest.mark.parametrize("batch_size", [1, 2])
def test_get_projects_suppliers_batching(storage, integration_data, batch_size):
    """Test batching behavior with different batch sizes using real storage."""
    with patch("bolfak.storage.bda_helper.MAX_ORG_IDS_PER_QUERY", batch_size):
        # Create a spy for the BDA get_async method
        get_async_spy = Mock(wraps=storage.bda.get_async)
        storage.bda.get_async = get_async_spy

        org_ids = {org["id"] for org in integration_data["orgs"]}
        project_ids = {project["id"] for project in integration_data["projects"]}
        suppliers = get_projects_suppliers(storage, project_ids)

        # Verify that the endpoint was called multiple times
        expected_calls = len(project_ids) // batch_size + (
            1 if len(project_ids) % batch_size else 0
        )
        assert get_async_spy.call_count == expected_calls

        # Verify we got all suppliers
        org_ids_found = {s["supplier_org_id"] for s in suppliers}
        assert org_ids_found == set(org_ids)
