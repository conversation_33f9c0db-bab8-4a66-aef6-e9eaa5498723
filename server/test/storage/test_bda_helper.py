import json
import pytest
from unittest.mock import Mock, patch

from bolfak.storage.bda_helper import get_multiple_entities_in_batches


def create_mock_storage_with_response(
    resources=None, ok=True, status_code=200, error_text=None
):
    """Helper function to create a mock storage with configured response."""
    mock_storage = Mock()
    mock_future = Mock()
    mock_future.result.return_value = Mock(ok=ok, status_code=status_code)
    if ok:
        mock_future.result.return_value.json.return_value = {
            "resources": resources or []
        }
    else:
        mock_future.result.return_value.text = error_text
    mock_storage.bda.get_async.return_value = mock_future
    return mock_storage


def create_mock_storage_with_batched_responses(responses):
    """Helper function to create a mock storage with multiple batched responses."""
    mock_storage = Mock()
    mock_futures = []
    for response in responses:
        mock_future = Mock()
        mock_future.result.return_value = Mock(ok=True)
        mock_future.result.return_value.json.return_value = {"resources": response}
        mock_futures.append(mock_future)
    mock_storage.bda.get_async.side_effect = mock_futures
    return mock_storage


def test_get_multiple_entities_in_batches_empty_query_ids():
    """Test that empty query_ids returns empty list."""
    mock_storage = create_mock_storage_with_response([{"id": "entity1"}])
    result = get_multiple_entities_in_batches(
        mock_storage, "test_entity", [], "test_key"
    )
    assert result == []
    assert not mock_storage.bda.get_async.called


def test_get_multiple_entities_in_batches_duplicate_query_ids():
    """Test that duplicate IDs are removed."""
    mock_storage = create_mock_storage_with_response([{"id": "entity1"}])
    result = get_multiple_entities_in_batches(
        mock_storage, "test_entity", ["id1", "id1", "id2"], "test_key"
    )
    assert result == [{"id": "entity1"}]

    # Check that the query was made with deduplicated IDs
    called_url = mock_storage.bda.get_async.call_args[0][0]
    assert called_url.count('id1') == 1


def test_get_multiple_entities_in_batches_single_batch():
    """Test fetching entities that fit in a single batch."""
    mock_storage = create_mock_storage_with_response([{"id": "entity1"}, {"id": "entity2"}])
    result = get_multiple_entities_in_batches(
        mock_storage, "test_entity", ["id1", "id2"], "test_key"
    )
    assert result == [{"id": "entity1"}, {"id": "entity2"}]
    mock_storage.bda.get_async.assert_called_once()


@patch("bolfak.storage.bda_helper.MAX_ORG_IDS_PER_QUERY", 2)
def test_get_multiple_entities_in_batches_multiple_batches():
    """Test batching with multiple requests."""
    responses = [
        [{"id": "entity1"}, {"id": "entity2"}],
        [{"id": "entity3"}, {"id": "entity4"}],
    ]
    mock_storage = create_mock_storage_with_batched_responses(responses)

    result = get_multiple_entities_in_batches(
        mock_storage, "test_entity", ["entity1", "entity2", "entity3", "entity4"], "test_key"
    )
    assert result == [
        {"id": "entity1"},
        {"id": "entity2"},
        {"id": "entity3"},
        {"id": "entity4"},
    ]
    assert mock_storage.bda.get_async.call_count == 2


def test_get_multiple_entities_in_batches_with_select_fields():
    """Test that the select parameter works correctly."""
    mock_storage = create_mock_storage_with_response([])
    select_fields = ["id", "name", "type"]

    get_multiple_entities_in_batches(
        mock_storage, "test_entity", ["id1"], "test_key", select=select_fields
    )

    called_url = mock_storage.bda.get_async.call_args[0][0]
    assert f'&show={json.dumps(select_fields)}' in called_url


def test_get_multiple_entities_in_batches_with_additional_filters():
    """Test that additional filters are included in the query."""
    mock_storage = create_mock_storage_with_response([])
    additional_filters = {"type": "test", "status": "active"}

    get_multiple_entities_in_batches(
        mock_storage,
        "test_entity",
        ["id1"],
        "test_key",
        additional_filters=additional_filters,
    )

    called_url = mock_storage.bda.get_async.call_args[0][0]
    assert '"type":"test"' in called_url.replace(" ", "")
    assert '"status":"active"' in called_url.replace(" ", "")


def test_get_multiple_entities_in_batches_error_handling():
    """Test that HTTP errors are properly handled."""
    mock_storage = create_mock_storage_with_response(
        ok=False, status_code=500, error_text="Internal Server Error"
    )

    with pytest.raises(RuntimeError) as exc:
        get_multiple_entities_in_batches(
            mock_storage, "test_entity", ["id1"], "test_key"
        )
    assert "Failed to fetch suppliers: 500 Internal Server Error" in str(exc.value)
