import configparser
from bolfak.clients.composite import Storage
from bolfak.featureflags import feature_active
from bolfak.fixtures.factories import create_default_project

# from bolfak.models import ACTIVE, CLOSED, SUPERVISOR_ROLE, MAIN_CONTRACTOR_ROLE
from bolfak.scripts.migrations.add_person_id_to_project_users import main


def test_add_person_id_to_project_users(
    storage: Storage,
    mocker,
    caplog,
    person_id_for_project_users_feature_flag_enabled_and_disabled,
    set_feature_flags_for_bda
):
    project1 = create_default_project(storage, 'Project 1')
    project2 = create_default_project(storage, 'Project 1')
    # The feature flag blocks creating new project_users having a user_account_id (the old property)
    # When seeding for the test, temporarily allow it to set up the necessary data for testing
    if feature_active('person_id_for_project_users'):
        set_feature_flags_for_bda({'person_id_for_project_users': False})
    user1 = storage.bda.post(
        '/project_users',
        json={
            'project_id': project1['id'],
            'role': 'member',
            'notify': False,
            'user_account_id': 'user_account_id_1',
            'represented_company_id': 'some_company_id',
        },
    ).json()
    user2 = storage.bda.post(
        '/project_users',
        json={
            'project_id': project2['id'],
            'role': 'member',
            'notify': False,
            'person_id': 'old_person_id',
            'user_account_id': 'user_account_id_1',
            'represented_company_id': 'some_other_company_id',
        },
    ).json()
    user3 = storage.bda.post(
        '/project_users',
        json={
            'project_id': project1['id'],
            'role': 'member',
            'notify': False,
            'user_account_id': 'user_account_id_2',
            'represented_company_id': 'some_company_id',
        },
    ).json()
    if feature_active('person_id_for_project_users'):
        set_feature_flags_for_bda({'person_id_for_project_users': True})
    config = configparser.RawConfigParser()
    mocker.patch(
        'bolfak.scripts.migrations.add_person_id_to_project_users.set_config',
        return_value=config,
    )
    client = mocker.MagicMock()

    def mock_get_user_account(user_account_id: str):
        mock_response = mocker.MagicMock()
        if user_account_id == 'user_account_id_1':
            mock_response.json.return_value = {
                'id': 'user_account_id_1', 'person_id': 'some_person_id'}
            mock_response.status_code = 200
        elif user_account_id is None or user_account_id == '' or user_account_id == 'None':
            raise Exception('User account id is None')
        return mock_response

    # Assign the function to the mock
    client.get_user_account = mock_get_user_account
    mocker.patch.object(storage, 'user_account_api', client)
    mocker.patch(
        'bolfak.scripts.migrations.add_person_id_to_project_users.setup_storage',
        return_value=storage,
    )

    main(['-c', '/dev/null'])

    user1_resp = storage.bda.get(f'/project_users/{user1["id"]}').json()
    assert user1_resp['person_id'] == 'some_person_id'
    user2_resp = storage.bda.get(f'/project_users/{user2["id"]}').json()
    assert user2_resp['person_id'] == 'some_person_id'
    user3_resp = storage.bda.get(f'/project_users/{user3["id"]}').json()
    assert user3_resp['person_id'] is None
    assert any(
        record.levelname == "ERROR"
        and f"Found project_user with no person_id. ID is {user3['id']}"
        in record.message
        for record in caplog.records
    )


def test_add_person_id_to_project_users_without_downtime(
    storage: Storage,
    mocker,
    caplog,
    set_feature_flags_for_bda,
    set_feature_flags
):
    # Mock setup
    config = configparser.RawConfigParser()
    mocker.patch(
        'bolfak.scripts.migrations.add_person_id_to_project_users.set_config',
        return_value=config,
    )
    client = mocker.MagicMock()

    def mock_get_user_account_before_insertions(user_account_id: str):
        mock_response = mocker.MagicMock()
        if user_account_id == 'user_account_id_1':
            mock_response.json.return_value = {
                'id': 'user_account_id_1', 'person_id': 'some_person_id'}
            mock_response.status_code = 200
        elif user_account_id is None or user_account_id == '' or user_account_id == 'None':
            raise Exception('User account id is None')
        return mock_response

    # Assign the function to the mock
    client.get_user_account = mock_get_user_account_before_insertions

    mocker.patch.object(storage, 'user_account_api', client)
    mocker.patch(
        'bolfak.scripts.migrations.add_person_id_to_project_users.setup_storage',
        return_value=storage,
    )

    # Ensure the relevant feature flag is disabled
    set_feature_flags_for_bda({'person_id_for_project_users': False})
    set_feature_flags({'person_id_for_project_users': False})
    project1 = create_default_project(storage, 'Project 1')
    user1 = storage.bda.post(
        '/project_users',
        json={
            'project_id': project1['id'],
            'role': 'member',
            'notify': False,
            'user_account_id': 'user_account_id_1',
            'represented_company_id': 'some_company_id',
        },
    ).json()

    # Run initial migration
    main(['-c', '/dev/null'])

    # Assume a new project user is created during the migration, but not part of it
    user2 = storage.bda.post(
        '/project_users',
        json={
            'project_id': project1['id'],
            'role': 'member',
            'notify': False,
            'user_account_id': 'user_account_id_2',
            'represented_company_id': 'some_company_id',
        },
    ).json()

    # Set feature flag to prevent using user_account_id
    set_feature_flags_for_bda({'person_id_for_project_users': True})
    set_feature_flags({'person_id_for_project_users': True})

    # So that this user cannot be created
    failed_creation_request = storage.bda.post(
        '/project_users',
        json={
            'project_id': project1['id'],
            'role': 'member',
            'notify': False,
            'user_account_id': 'user_account_id_2',
            'represented_company_id': 'some_company_id',
        },
        ignore_errors=True
    )
    assert failed_creation_request.status_code >= 400

    # But this one can be created
    user3_resp = storage.bda.post(
        '/project_users',
        json={
            'project_id': project1['id'],
            'role': 'member',
            'notify': False,
            'person_id': 'person_id_3',
            'represented_company_id': 'some_company_id',
        }
    )
    assert user3_resp.status_code == 201 or user3_resp.status_code == 200
    user3 = user3_resp.json()

    # Finally, run the migration again and validate the output
    def mock_get_user_account_after_insertions(user_account_id: str):
        mock_response = mocker.MagicMock()
        if user_account_id == 'user_account_id_1':
            mock_response.json.return_value = {
                'id': 'user_account_id_1',
                'person_id': 'person_id_1'
            }
            mock_response.status_code = 200
        elif user_account_id == 'user_account_id_2':
            mock_response.json.return_value = {
                'id': 'user_account_id_2',
                'person_id': 'person_id_2'
            }
            mock_response.status_code = 200
        elif user_account_id is None or user_account_id == '' or user_account_id == 'None':
            raise Exception('User account id is None')
        return mock_response

    # Assign the function to the mock
    client.get_user_account = mock_get_user_account_after_insertions
    main(['-c', '/dev/null'])

    user1_resp = storage.bda.get(f'/project_users/{user1["id"]}').json()
    assert user1_resp['person_id'] == 'person_id_1'
    user2_resp = storage.bda.get(f'/project_users/{user2["id"]}').json()
    assert user2_resp['person_id'] == 'person_id_2'
    user3_resp = storage.bda.get(f'/project_users/{user3["id"]}').json()
    assert user3_resp['person_id'] == 'person_id_3'
    assert all(
        record.levelname != "ERROR"
        for record in caplog.records
    )
