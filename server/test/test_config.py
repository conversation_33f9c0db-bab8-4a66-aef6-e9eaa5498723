from bolfak.config import get_visitor_whitelist_org_ids, set_config, reset_config


class TestGetVisitorWhitelistOrgIds:
    """Test cases for get_visitor_whitelist_org_ids function."""

    def setup_method(self):
        self.test_config = {
            'monitoring': {
                'visitor_whitelist_org_ids': ''
            }
        }

    def teardown_method(self):
        reset_config()

    def test_empty_whitelist_returns_empty_list(self, config):
        config.set('monitoring', 'visitor_whitelist_org_ids', '')
        result = get_visitor_whitelist_org_ids(config)
        assert result == []

    def test_single_org_id_returns_single_item_list(self, config):
        config.set('monitoring', 'visitor_whitelist_org_ids', 'org123')
        result = get_visitor_whitelist_org_ids(config)
        assert result == ['org123']

    def test_multiple_org_ids_returns_list(self, config):
        config.set('monitoring', 'visitor_whitelist_org_ids', 'org123,org456,org789')
        result = get_visitor_whitelist_org_ids(config)
        assert result == ['org123', 'org456', 'org789']

    def test_whitespace_is_stripped(self, config):
        config.set('monitoring', 'visitor_whitelist_org_ids', '  org123  ,  org456  ,  org789  ')
        result = get_visitor_whitelist_org_ids(config)
        assert result == ['org123', 'org456', 'org789']

    def test_empty_items_are_filtered_out(self, config):
        config.set('monitoring', 'visitor_whitelist_org_ids', 'org123,,org456,')
        result = get_visitor_whitelist_org_ids(config)
        assert result == ['org123', 'org456']

    def test_missing_monitoring_section_returns_empty_list(self, config):
        if config.has_section('monitoring'):
            config.remove_section('monitoring')
        result = get_visitor_whitelist_org_ids(config)
        assert result == []

    def test_missing_visitor_whitelist_key_returns_empty_list(self, config):
        # Ensure monitoring section exists but without the key
        if not config.has_section('monitoring'):
            config.add_section('monitoring')
        result = get_visitor_whitelist_org_ids(config)
        assert result == []

    def test_none_config_uses_global_config(self):
        self.test_config['monitoring']['visitor_whitelist_org_ids'] = 'org123,org456'
        set_config(self.test_config)

        result = get_visitor_whitelist_org_ids(None)
        assert result == ['org123', 'org456']

    def test_custom_config_parameter(self):
        custom_config = {
            'monitoring': {
                'visitor_whitelist_org_ids': 'custom123,custom456'
            }
        }
        set_config(custom_config)

        result = get_visitor_whitelist_org_ids()
        assert result == ['custom123', 'custom456']

    def test_complex_org_ids_with_hyphens_and_numbers(self, config):
        config.set('monitoring', 'visitor_whitelist_org_ids', '123456-789,ABC-123-DEF,999888777')
        result = get_visitor_whitelist_org_ids(config)
        assert result == ['123456-789', 'ABC-123-DEF', '999888777']

    def test_single_space_between_commas(self, config):
        config.set('monitoring', 'visitor_whitelist_org_ids', 'org123, org456,org789')
        result = get_visitor_whitelist_org_ids(config)
        assert result == ['org123', 'org456', 'org789']
