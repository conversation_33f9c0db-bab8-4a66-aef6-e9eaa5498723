import pytz
import datetime

from qvarnclient import QvarnRequests
from qvarnclient import QvarnClient
from stv.data.clients.qvarn.qvarnapi import QvarnApi
import stv.data.dal.qvarn.organization as organisation
import stv.data.dal.qvarn.user_account as user_account
import stv.data.dal.qvarn.customership as cs
import stv.data.dal.qvarn.person as person
import stv.data.dal.qvarn.service as service
from robot.libraries.BuiltIn import BuiltIn
from robot.api import logger

from .utils import _get_config_data


class QvarnApiClient(QvarnRequests):
    def __init__(self, base_url, client_id, client_secret, scopes):
        self.qvarn_api = QvarnApi(QvarnClient(base_url=base_url, requests=self))
        self.service_provider = 'tilaajavastuu'
        self.service_key = 'bolagsfakta'
        super().__init__(
            base_url=base_url,
            client_id=client_id,
            client_secret=client_secret,
            scopes=scopes,
            verify_requests=True,
            max_workers=1,
            timeout=None,
            token=None,
            default_headers={'Qvarn-Why': 'initializing test data'},
        )

    def setup_user_data(self, config_file):
        """
        Setup persons, orgs and contracts to Qvarn and Gluu

        :param config_file: path to user configuration file, data in json containing users key
        :rtype: dictionary
        """
        data = _get_config_data(config_file)

        updated_users = {}
        created_orgs = {}
        for user in data['users']:
            user = self._create_person(user=user)
            user = self._create_contract(user=user)
            if user['org_number'] not in created_orgs:
                user = self._create_organisation(user=user)
                created_orgs[user['org_number']] = user['org_id']
            else:
                user['org_id'] = created_orgs[user['org_number']]
            user = self._add_user_to_org(user=user)
            updated_users[user['username']] = user
        return updated_users

    def _create_person(self, user):
        contact_source = 'self'
        person_data = person.create_full_person(
            qvarn=self.qvarn_api,
            first_name=user['first_name'],
            last_name=user['last_name'],
            email=user['email'],
            phone=user['phone'],
            contact_source=contact_source,
            verified_email=True,
        )
        now = datetime.datetime.now(pytz.utc).replace(microsecond=0)
        person_data.contacts.change_email_verification_timestamp(
            value=now.isoformat(), source=contact_source
        )
        person_data = person.update_full_person(qvarn=self.qvarn_api, person=person_data)
        user['person_id'] = person_data.id
        logger.info('{} user created to Qvarn'.format(user['username']))

        return user

    def update_user_gluu_data(self, user):
        """
        Update person ID to Gluu

        :param user: user dict
        """
        scim = BuiltIn().get_library_instance('scim')
        gluu_user = scim.create_gluu_user(user['username'], user['password'], user['person_id'])

        person_data = person.get_full_person(qvarn=self.qvarn_api, person_id=user['person_id'])
        person_data.gluu_user_id = gluu_user.id
        person.update_full_person(qvarn=self.qvarn_api, person=person_data)
        logger.info('{} user created to Gluu'.format(user['username']))

    def _create_organisation(self, user):
        org = organisation.create_org(
            qvarn=self.qvarn_api,
            name=user['org_name'],
            country=user['country'],
            number=user['org_number'],
            service_provider=self.service_provider,
        )

        if user['set_address_and_invoicing']:
            org.billing_contacts.change_email(
                value=user['org_invoicing_email_address'],
                source='self',
                service_provider=self.service_provider,
            )

            org.contacts.change_address_street_address(
                user['address'], user['person_id'], self.service_provider
            )
            org.contacts.change_address_postal_code(
                user['postal_code'], user['person_id'], self.service_provider
            )
            org.contacts.change_address_city(user['city'], user['person_id'], self.service_provider)
            org.contacts.change_address_country(
                user['country'], user['person_id'], self.service_provider
            )

            org = organisation.update_org(
                qvarn=self.qvarn_api, org=org, service_provider=self.service_provider
            )
        user['org_id'] = org.id

        # Customership contracts in Qvarn are replaced by customership contract API.
        # The code below is outdated.
        customership = cs.create_customership(
            qvarn=self.qvarn_api,
            org_id=org.id,
            person_id=user['person_id'],
            contract_type='customership',
            service_provider=self.service_provider,
            mark_tos_accepted=True,
            tos_version='v1',
            tos_language='en',
        )

        customership.change_contract_state(contract_state='active', person_id=user['person_id'])
        cs.update_customership(qvarn=self.qvarn_api, customership=customership)
        user['customership_id'] = customership.id

        logger.info('{} organisation created to Qvarn'.format(user['org_name']))
        return user

    def _create_contract(self, user):
        contract = user_account.create_user_account_without_representations(
            qvarn=self.qvarn_api,
            person_id=user['person_id'],
            user_name=user['username'],
            preferred_language="en",
            contract_type='user_account',
            service_provider=self.service_provider,
        )

        user['contract'] = contract
        logger.info('{} contract created to Qvarn'.format(user['username']))
        return user

    def _add_user_to_org(self, user):
        if "global_permissions" in user:
            global_permissions_list = user["global_permissions"]
        else:
            global_permissions_list = []
        user['contract'].add_representation(
            org_id=user['org_id'],
            global_permissions=global_permissions_list,
            permissions=user['permissions'],
            user_role=user['role']
        )
        user['contract'] = user_account.update_user_account(
            qvarn=self.qvarn_api, user_account=user['contract']
        )
        logger.info('{} added to org'.format(user['username']))
        return user

    def create_services(self, config_file):
        """
        Create services to Qvarn

        :param config_file: path to user configuration file, data in json containing services key
        """
        data = _get_config_data(config_file)
        for service_data in data['services']:
            if service.get_service_by_key(
                qvarn=self.qvarn_api,
                service_key=service_data['service_key'],
                service_provider=self.service_provider,
            ):
                logger.info('{} service already exists'.format(service_data['service_key']))
            else:
                created_service = self.qvarn_api.create('services', service_data)
                logger.info('{} service added'.format(service_data['service_key']))

                self._create_service_plan(data, created_service['id'])

    def _create_service_plan(self, data, service_id):
        for service_plan_data in data['service_plans']:
            service_plan_data['service_id'] = str(service_id)
            self.qvarn_api.create('service_plans', service_plan_data)
            logger.info('{} service plan added'.format(service_plan_data['dependencies']))

    def delete_report_accesses(self, countries):
        """
        Delete report accesses from Qvarn.

        :param countries: comma separated list of countries, example FI,EE
        """
        report_access_ids = []
        for country in countries.split(','):
            report_access_ids.extend(
                self.qvarn_api.search('report_accesses', country__exact=country)
            )

        for report_access_id in report_access_ids:
            self.qvarn_api.delete('report_accesses', id=report_access_id)

        logger.info('Report accesses deleted.')

    def get_order_reference(self, user):
        """
        Get order reference from Qvarn.

        :param user: user dict
        :rtype: string
        """
        # Customership contracts in Qvarn are replaced by customership contract API.
        # The code below is outdated.
        customership = cs.get_customership_by_id(
            qvarn=self.qvarn_api, customership_id=user['customership_id']
        )
        for subscription in customership.data['service_subscriptions']:
            if subscription['service_state'] == 'ordered':
                logger.info('Reference is {}'.format(subscription['customer_reference']))
                return subscription['customer_reference']
        BuiltIn().fail('Reference could not be found.')

    def delete_subscriptions(self, user):
        """
        Delete user's subscriptions from Qvarn.

        :param user: user dict
        """
        # Customership contracts in Qvarn are replaced by customership contract API.
        # The code below is outdated.
        customership = cs.get_customership_by_id(
            qvarn=self.qvarn_api, customership_id=user['customership_id']
        )
        customership.data['service_subscriptions'] = []
        customership.data['invoicing'] = []
        cs.update_customership(qvarn=self.qvarn_api, customership=customership)

    def get_stored_price(self, user):
        """
        Get stored subscription price from Qvarn.

        :param user: user dict
        :rtype: int
        """
        # Customership contracts in Qvarn are replaced by customership contract API.
        # The code below is outdated.
        customership = cs.get_customership_by_id(
            qvarn=self.qvarn_api, customership_id=user['customership_id']
        )
        if len(customership.data['invoicing']) != 1:
            BuiltIn().fail(
                'Unexpected invoice count. Expected: 1, actual: {}'.format(
                    len(customership.data['invoicing'])
                )
            )
        price = customership.data['invoicing'][0]['price']
        logger.info('Price is {} ({} in Qvarn)'.format(int(price) / 100, price))
        return int(price) / 100

    def wait_report_access_in_qvarn(self, timestamp, country, timeout=5):
        """
        Wait that report access is stored to Qvarn

        :param timestamp: waited report access is stored after timestamp, open_report keyword
                          returns the timestamp
        :param country: report country
        :param timeout for waiting (optional)
        :rtype: str
        """
        start_time = datetime.datetime.now()
        while start_time + datetime.timedelta(seconds=timeout) > datetime.datetime.now():
            report_accesses = self.qvarn_api.search(
                'report_accesses', show_all=True, country__exact=country, status__not_equal='hidden'
            )
            for access in report_accesses:
                if access['access_time'] >= timestamp:
                    logger.info('Report access found.')
                    return access['access_time']

        BuiltIn().fail('Report access was not found!')

    def wait_subscription_in_qvarn(self, customership_id, timeout=5):
        """
        Wait that subscription is stored to Qvarn

        :param customership_id: customership id for the subscription
        :param timeout for waiting (optional)
        """
        start_time = datetime.datetime.now()
        while start_time + datetime.timedelta(seconds=timeout) > datetime.datetime.now():
            # Customership contracts in Qvarn are replaced by customership contract API.
            # The code below is outdated.
            contract = cs.get_customership_by_id(
                qvarn=self.qvarn_api, customership_id=customership_id
            )
            if contract.service_subscriptions:
                logger.info('Subscription found.')
                return

        BuiltIn().fail('Subscription was not found!')

    def get_organisation_data(self, user, data):
        """
        Get data from user's organisation

        :param user: user dict
        :param data to get
        :rtype: str or list
        """
        org = organisation.get_full_org(
            qvarn=self.qvarn_api, org_id=user['org_id'], service_provider=self.service_provider
        )
        return org.data[data]

    def create_report_access(self, archive_id, country, active_org_id, identifier):
        """
        Create report access to Qvarn.

        :param archive_id: report archive_id
        :param country: country with two letters
        :param active_org_id: active company's org id
        :param identifier: report company's org id
        :param access_time: report access time
        """
        self.qvarn_api.create(
            'report_accesses',
            {
                'access_time': datetime.datetime.utcnow().replace(microsecond=0).isoformat()
                               + '+00:00',
                'arkisto_id': archive_id,
                'client_id': None,
                'customer_org_id': active_org_id,
                'gov_org_ids': [
                    {
                        'country': country,
                        'gov_org_id': identifier,
                        'org_id_type': 'registration_number',
                    }
                ],
                'org_id': None,
                'report_id': None,
                'person_id': None,
                'status': 'active',
            },
        )

        logger.info('Archived report created.')
