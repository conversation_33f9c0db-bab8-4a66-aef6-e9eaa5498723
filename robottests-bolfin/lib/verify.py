from robot.libraries.BuiltIn import BuiltIn
from robot.api import logger

import resources.aliases as alias


class Verify(object):
    @staticmethod
    def verify_search_result(search_text, country):
        """
        Verify company search result.

        :param search_text: search string
        :param country: search country
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        selenium.wait_until_element_contains(
            locator=alias.COMPANY_ELEMENT['company-list-row'], text=search_text
        )
        search_result_count = selenium.get_element_count(
            locator=alias.COMPANY_ELEMENT['company-list-row']
        )
        company_list = []
        for row in range(1, search_result_count + 1):
            selenium.table_row_should_contain(
                locator=alias.COMPANY_ELEMENT['company-list-row'], row=row, expected=search_text
            )
            selenium.table_row_should_contain(
                locator=alias.COMPANY_ELEMENT['company-list-row'],
                row=row,
                expected=alias.LANG_TO_UN[country],
            )
            company_list.append(
                selenium.get_table_cell(
                    locator=alias.COMPANY_ELEMENT['company-table'], row=row + 1, column=1
                )
            )

        if company_list != sorted(company_list):
            BuiltIn().fail('Company list is not in alphabetical order.')

        logger.info('Search result verified.')

    @staticmethod
    def verify_company_list_is_empty():
        """
        Verify company list is empty.
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        if selenium.get_element_count(locator=alias.COMPANY_ELEMENT['company-list-row']) != 1:
            BuiltIn().fail('Company list is not empty.')

    @staticmethod
    def verify_company_search_field_is_empty():
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        selenium.table_row_should_contain(
            locator=alias.COMPANY_ELEMENT['company-list-row'],
            row=1,
            expected='Please enter a query',
        )

    @staticmethod
    def verify_company_filtering(filter_text):
        """
        Verify company filtering in Search history page.

        :param filter_text: filtering string
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        search_result_count = selenium.get_element_count(
            locator=alias.COMPANY_ELEMENT['company-list-row']
        )
        for row in range(1, search_result_count + 1):
            selenium.table_row_should_contain(
                locator=alias.COMPANY_ELEMENT['company-list-row'], row=row, expected=filter_text
            )

    @staticmethod
    def verify_company_list_is_filled(user):
        """
        Verify company list is filled.

        :param user: user dict
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        for company in user['my_companies']:
            selenium.table_should_contain(
                locator=alias.COMPANY_ELEMENT['company-list-row'], expected=company['org_id']
            )
            selenium.table_should_contain(
                locator=alias.COMPANY_ELEMENT['company-list-row'], expected=company['status']
            )
            selenium.table_should_contain(
                locator=alias.COMPANY_ELEMENT['company-list-row'], expected=company['country']
            )

    @staticmethod
    def verify_reliable_partner_logo_is_shown(base_url):
        """
        Verify Reliable Partner logo is shown in first row in company list

        :param base_url: BOL servce's base url
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        element = selenium.get_webelement(alias.COMPANY_ELEMENT['company-list-reliable-partner'])
        expected_url = base_url + alias.IMAGE['reliable-partner']
        selenium.wait_until_element_is_visible(
            alias.COMPANY_ELEMENT['company-list-reliable-partner'])
        actual_url = element.find_element_by_tag_name('img').get_attribute('src')
        if actual_url != expected_url:
            BuiltIn().fail(
                'Unexpected url for image. Expected: {}, actual: {}.'.format(
                    expected_url, actual_url
                )
            )

        logger.info('Reliable Partner logo verified.')

    @staticmethod
    def verify_reliable_partner_check_mark_is_shown():
        """
        Verify Reliable Partner check mark is shown in first row in company list
        when company view is open
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        element_class = selenium.get_webelement(
            alias.COMPANY_ELEMENT['company-list-rp-check-mark']
        ).get_attribute('class')
        if element_class != alias.CSS['rp-check-mark']:
            BuiltIn().fail(
                'Reliable Partner check mark not shown (class = "{}" expected)'.format(
                    alias.CSS['rp-check-mark']
                )
            )

        logger.info('Reliable Partner check mark verified.')

    @staticmethod
    def verify_checkbox_state(locator, expected_state):
        """
        Verify that checkbox is in expected state

        :param locator: locator for checkbox
        :param expected_state: expected state (checked or unchecked)
        """
        states = {'checked': True, 'unchecked': False}
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        if states[expected_state] != selenium.get_webelement(locator).is_selected():
            BuiltIn().fail('Checkbox was not {}'.format(expected_state))
        logger.info('Checkbox state verified.')

    @staticmethod
    def verify_environment_dropdown_menu_services():
        """
        Verify that environment dropdown menu contains all services
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        link_count = selenium.get_element_count(alias.ENVIRONMENT_DROPDDDOWN['services'])
        if link_count != len(alias.ENVIRONMENT_LINKS):
            BuiltIn().fail(
                'Expected: {} links, actual: {} links.'.format(
                    len(alias.ENVIRONMENT_LINKS), link_count
                )
            )
        for locator in alias.ENVIRONMENT_LINKS.values():
            selenium.wait_until_element_is_visible(locator)
        logger.info('Environment dropdown menu services verified.')

    @staticmethod
    def verify_message_contains(message_type, text, timeout=None):
        """
        Wait and verify that message contains certain text

        :param message_type: type of message (success, warning, danger)
        :param text: expected text included in message
        :param timeout: timeout for message to appear (optional)
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        if not timeout:
            timeout = selenium.get_selenium_timeout()
        locator = alias.TEMPLATE_ELEMENT['{}-alert'.format(message_type)]
        selenium.wait_until_element_is_visible(locator, timeout=timeout)
        for element in selenium.get_webelements(locator):
            if text in element.text:
                logger.info('Message "{}" found.'.format(text))
                return
        BuiltIn().fail('{} message "{}" was not found.'.format(message_type, text))

    @staticmethod
    def verify_unknown_listed_company(company_id):
        """
        Verify that "Unknown" and company ID can be found from company list

        :param company_id: company ID for unknown company
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        search_result_count = selenium.get_element_count(
            locator=alias.COMPANY_ELEMENT['company-list-row']
        )
        for row in range(1, search_result_count + 1):
            row_element = selenium.get_webelement(
                '{}[{}]'.format(alias.COMPANY_ELEMENT['company-list-row'], row)
            )
            if company_id in row_element.text and 'Unknown' in row_element.text:
                logger.info('Unknown company verified.')
                return
        BuiltIn().fail('Unknown and {} not found!'.format(company_id))

    @staticmethod
    def verify_archived_report_details(expected_archive_id_list, expected_status_list):
        """
        Verify archived reports details (namely: status and archive ID)

        :param expected_archive_id_list: list of expected archive ids
        :param expected_status_list: list of expected statuses

        NOTE: the number of expected statuses needs to match the number of expected archive ids
        """

        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        BuiltIn().should_be_equal_as_integers(len(expected_status_list),
                                              len(expected_archive_id_list),
                                              msg="Number of expected statuses did not match number of expected archive ids!")
        for index, archive_id in enumerate(expected_archive_id_list):
            selenium.page_should_contain_element("//td[text()='" + archive_id + "']",
                                                 message="Could not find archived report with id: "
                                                         + archive_id)
            actual_status = selenium.get_element_attribute(
                "//td[text()='" + archive_id + "']/..//i",
                "title")
            BuiltIn().should_be_equal_as_strings(actual_status, expected_status_list[index],
                                                 ignore_case=True,
                                                 msg="Status mismatch for archived report with id: "
                                                     + archive_id + " >> ")

    @staticmethod
    def _verify_rala_logo_is_shown(language, rala_logo_type, rala_image_type):
        """
        Verify RALA logo is shown in first row in company list and/or company view is open
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        actual_src = selenium.get_webelement(
            alias.COMPANY_ELEMENT[rala_logo_type]
        ).get_attribute('src')
        expected_src = "/static/img/" + rala_image_type + "_" + str(language).lower() + ".jpg"
        if expected_src not in actual_src:
            BuiltIn().fail(
                'RALA logo wasnt found with right language (expected src="{}", found src="{}")'.format(
                    expected_src, actual_src)
            )
        logger.info('RALA logo: {} verified.'.format(expected_src))

    def verify_rala_competence_logo_is_shown(self, language):
        self._verify_rala_logo_is_shown(language, 'rala-competence-logo', 'rala_comp')

    def verify_rala_certificate_logo_is_shown(self, language):
        self._verify_rala_logo_is_shown(language, 'rala-certificate-logo', 'rala_cert')
