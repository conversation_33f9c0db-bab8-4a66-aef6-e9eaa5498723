from stv.data.clients.scim.scim import ScimClient
import stv.data.dal.scim.gluu_user as gluu

from robot.api import logger


class ScimApiClient(ScimClient):
    _gluu_resource_id = '1203.825aa654-a95d-4195-a75d-1d6720ed357b'

    def __init__(self, gluu_server, client_id, client_secret):
        super(ScimApiClient, self).__init__(
            gluu_server, self._gluu_resource_id, True, client_id, client_secret
        )

    def create_gluu_user(self, username, password, person_id):
        """
        Create person to Gluu

        :param username: user's username
        :param password: user's password
        :param person_id: user's person id
        """
        user = gluu.get_gluu_user_by_username(self, username)
        if user:
            gluu.delete_gluu_user(self, user.id)

        return gluu.create_gluu_user(self, username, password, True, person_id)
