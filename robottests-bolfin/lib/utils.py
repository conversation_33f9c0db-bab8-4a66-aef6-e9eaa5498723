import datetime
import time
import os.path
import json
import requests
from urllib.parse import quote, urljoin

from robot.libraries.BuiltIn import BuiltIn
from robot.api import logger

import resources.aliases as alias


def _get_config_data(config_file):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    with open(os.path.join(current_dir, '..', config_file), encoding='utf-8') as json_data:
        return json.load(json_data)


class Utils(object):
    @staticmethod
    def close_report_windows_and_company_view():
        """
        Close all report windows and close company view if open
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')

        # Close all windows except MAIN window
        for handle in selenium.get_window_handles()[1:]:
            selenium.select_window(handle)
            selenium.close_window()
            logger.info('Window closed.')

        selenium.select_window('MAIN')

        if selenium.get_element_count(alias.COMPANY_ELEMENT['company-view']) != 0:
            BuiltIn().run_keyword('Close company view')

    def open_reports(self, user, country, language=''):
        """
        Open company reports in search page to get them to company list in Search history page
        List of companies are from user configuration

        :param user: user dict
        :param country: country for the companies
        """
        for company in user['my_companies']:
            if company['country'] == country:
                self.open_report(org_id=company['org_id'], company=company['name'],
                                 country=country, language=company['language'] or language)

    def open_report(self, org_id, company, country, language=''):
        """
        Open company report in search page to get them to company list in Search history page

        :param org_id: business ID for company
        :param company: company name
        :param country: country in UN format
        :rtype: string
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        BuiltIn().run_keyword('Start loading spinner')
        timestamp = datetime.datetime.utcnow().replace(microsecond=0).isoformat() + '+00:00'
        selenium.click_element(alias.COMPANY_ELEMENT['company'].format(string_text=org_id))
        selenium.wait_until_page_does_not_contain_element(alias.TEMPLATE_ELEMENT['loading-spinner'],
                                                          timeout=20)
        selenium.wait_until_element_is_visible(alias.COMPANY_ELEMENT['company-view'])
        selenium.wait_until_element_is_visible(alias.COMPANY_ELEMENT['view-latest-report-button'])
        windows = selenium.get_window_handles()
        selenium.click_element(alias.COMPANY_ELEMENT['view-latest-report-button'])
        selenium.click_element(alias.COMPANY_ELEMENT['view-latest-fin-report-button'])
        self.wait_new_window_is_opened(windows)
        selenium.select_window('NEW', 5)
        url_contains = self.get_expected_report_url_segment(
            'latest', country, org_id, company, archive_id='', language=language
        )
        selenium.wait_for_condition('return document.URL.indexOf("{}") > -1'.format(url_contains))
        selenium.select_window('MAIN')
        logger.info('Company {} report opened.'.format(org_id))
        return timestamp

    def click_report_icon_in_company_list(self, company_id):
        """
        Finds company from company list by company id. Click company's report icon.

        :param company_id: company registration id
        """
        self._find_and_click_icon(
            search_text=company_id,
            aliases={
                'table': 'company-table',
                'table-row': 'company-list-row',
                'icon': 'report-icon-link',
            },
            header_rows=1,
            search_column=1,
            click_column=3,
        )

    def click_report_icon_in_archived_list(self, archive_id):
        """
        Finds report from archived list by archive id. Click report icon.

        :param archive_id: archive id
        """
        self._find_and_click_icon(
            search_text=archive_id,
            aliases={
                'table': 'archived-table',
                'table-row': 'archived-table-row',
                'icon': 'archive-report-icon',
            },
            header_rows=1,
            search_column=2,
            click_column=4,
        )

    def _find_and_click_icon(self, search_text, aliases, header_rows, search_column, click_column):
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')

        # row and column in get_table_cell start from 1
        for row in range(
            1, len(selenium.get_webelements(alias.COMPANY_ELEMENT[aliases['table-row']])) + 1
        ):
            if search_text in selenium.get_table_cell(
                locator=alias.COMPANY_ELEMENT[aliases['table']],
                row=row + header_rows,
                column=search_column,
            ):
                selenium.wait_until_element_is_visible(
                    alias.COMPANY_ELEMENT[aliases['icon']].format(row=row, column=click_column))
                selenium.click_element(
                    alias.COMPANY_ELEMENT[aliases['icon']].format(row=row, column=click_column)
                )
                logger.info('Report icon clicked.')
                return
        BuiltIn().fail('Could not find row. Searching for {}'.format(search_text))

    @staticmethod
    def close_user_dropdown_menu():
        """
        Close user dropdown menu if it's open
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        if selenium.get_webelement(alias.USER_DROPDOWN['sign-out']).is_displayed():
            selenium.click_element(alias.TEMPLATE_ELEMENT['user-menu'])
            selenium.wait_until_element_is_not_visible(alias.USER_DROPDOWN['sign-out'])
            logger.info('User menu closed.')
        else:
            logger.info('User menu was already closed.')

    @staticmethod
    def close_environment_dropdown_menu():
        """
        Close environment dropdown menu if it's open
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        if selenium.get_webelement(alias.ENVIRONMENT_LINKS['home']).is_displayed():
            selenium.click_element(alias.TEMPLATE_ELEMENT['environment-menu'])
            selenium.wait_until_element_is_not_visible(alias.ENVIRONMENT_LINKS['home'])
            logger.info('Environment menu closed.')
        else:
            logger.info('Environment menu was already closed.')

    @staticmethod
    def set_checkbox_unchecked(checkbox):
        """
        Makes sure that checkbox is unchecked

        :param checkbox: checkbox element alias
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        if selenium.get_webelement(
            alias.SUBSCRIPTION_PAGE['{}-status'.format(checkbox)]
        ).is_selected():
            selenium.click_element(alias.SUBSCRIPTION_PAGE[checkbox])
            logger.info('{} unselected.'.format(checkbox))
        else:
            logger.info('{} was already unselected.'.format(checkbox))

    @staticmethod
    def set_checkbox_checked(checkbox):
        """
        Makes sure that checkbox is checked

        :param checkbox: checkbox element alias
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        if not selenium.get_webelement(
            alias.SUBSCRIPTION_PAGE['{}-status'.format(checkbox)]
        ).is_selected():
            selenium.click_element(alias.SUBSCRIPTION_PAGE[checkbox])
            logger.info('{} selected.'.format(checkbox))
        else:
            logger.info('{} was already selected.'.format(checkbox))

    @staticmethod
    def get_archive_id(user, company_id):
        """
        Get archive id for a company's report

        :param user: user dict
        :param company_id: company's business ID
        :rtype: string
        """
        for company in user['my_companies']:
            if company['org_id'] == company_id:
                return company['archive_id']

    def set_subscription_canceled(self, go_after_page=None):
        """
        Makes sure that Report PRO service subscription is not active

        :param go_after_page: name of page where to go after canceling subscription
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        BuiltIn().run_keyword('Go to "subscription" page')
        if len(selenium.get_webelements(alias.SUBSCRIPTION_PAGE['terminate-button'])) != 0:
            self.set_checkbox_checked('terminate-checkbox')
            selenium.wait_until_element_is_visible(
                alias.SUBSCRIPTION_PAGE['terminate-warning-checkbox']
            )
            selenium.click_element(alias.SUBSCRIPTION_PAGE['terminate-warning-checkbox'])
            self.set_checkbox_checked('terminate-warning-checkbox')
            selenium.click_element(alias.SUBSCRIPTION_PAGE['terminate-button'])
            selenium.wait_until_page_contains(alias.ELEMENT_TEXT['subscribe-button'])
            logger.info('Subscription canceled.')
        else:
            logger.info('Subscription was already canceled.')

        if go_after_page:
            BuiltIn().run_keyword('Go to "{}" page'.format(go_after_page))

    @staticmethod
    def get_shifted_date(shift):
        """
        Get date relating to current date

        :param shift: days to shift (examples: -1, +1)
        :rtype: datetime
        """
        return (datetime.date.today() + datetime.timedelta(days=int(shift))).isoformat()

    @staticmethod
    def set_multiple_test_variables(variables):
        """
        Set multiple test variables from a list

        :param variables: comma (,) separated list of variables,
                          variable name and value are separated with colon (:).
                          Example: SEARCH_TEXT:company,COUNTRY_UN:SWE
        """
        for variable in variables.split(','):
            name_value_list = variable.split(':')
            BuiltIn().set_test_variable('${{{}}}'.format(name_value_list[0]), name_value_list[1])

    @staticmethod
    def get_iso_timestamp():
        """
        Get current time in iso format

        :rtype: datetime
        """
        return datetime.datetime.utcnow().replace(microsecond=0).isoformat() + '+00:00'

    def open_reports_for_search_history_load_test(self, config_file):
        """
        Open reports which are used for Search history page load test

        :param config_file: path to user configuration file, data in json containing users key
        """
        data = _get_config_data(config_file)
        for report in data['performance']:
            self.open_report(report['org_id'], report['name'],
                             report['country'], report['language'])
        self.close_report_windows_and_company_view()

    @staticmethod
    def wait_new_window_is_opened(windows):
        """
        Wait new window to be opened

        :param windows: list of windows open before action to open new window
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        end_time = datetime.datetime.now() + datetime.timedelta(seconds=3)
        while selenium.get_window_handles() == windows and end_time > datetime.datetime.now():
            time.sleep(0.02)
        if selenium.get_window_handles() == windows:
            BuiltIn().fail('New window was not opened.')

    def wait_until_timestamp(self, timestamp, added_seconds=0):
        """
        Wait until current time is timestamp plus optional added seconds

        :param timestamp: waited time in iso format
        :param added_seconds: seconds added to waited time (optional)
        """
        timestamp_in_datetime = datetime.datetime.strptime(timestamp, "%Y-%m-%dT%H:%M:%S+00:00")
        until = (timestamp_in_datetime + datetime.timedelta(seconds=added_seconds)).isoformat()
        while until > self.get_iso_timestamp():
            time.sleep(0.02)

    @staticmethod
    def get_expected_report_url_segment(category, country, org_id,
                                        company, archive_id='', language=''):
        """
        Get segment of expected report url containing report file name

        :param category: report category, latest or archived
        :param country: country for the company in UN format
        :param org_id: organisation ID for the company
        :param company: name of the company
        :param archive_id: archive id for report (optional: only for archived reports)
        :param language: language code of web report template
        :rtype: str
        """
        return quote(
            alias.REPORT_URL[category].format(
                country=country,
                org_id=org_id,
                language=language,
                company=company,
                archive_id=archive_id,
                date=datetime.date.today().isoformat(),
            ), safe="'/"
        )

    def handle_datepicker_month_view(self, filter_type, number_of_days=1):
        """
        Change datepicker month if needed and return locator for day
        Day is decided based on current date and filter argument
        filter_type = start -> current day + number_of_days
        filter_type = end -> current day - number_of_days

        :param filter_type: start or end
        :param number_of_days: the number of days in the future/past to go to (default: 1)
        :rtype: str
        """
        shift = {'start': int(number_of_days), 'end': -int(number_of_days)}
        shifted_date = self.get_shifted_date(shift=shift[filter_type])
        if datetime.date.today().month != shifted_date[5:-3].lstrip('0'):
            element = {'start': 'next', 'end': 'prev'}
            selenium = BuiltIn().get_library_instance('SeleniumLibrary')
            selenium.click_element(
                alias.COMPANY_ELEMENT['datepicker-{}-month'.format(element[filter_type])]
            )
        return alias.COMPANY_ELEMENT['datepicker-date'].format(day=shifted_date[-2:].lstrip('0'))

    def ensure_language(self, lang):
        """Make sure, that active language is `lang`.

        If active language is already `lang`, then do nothing.

        If active language is not `lang`, then change language to `lang`.

        Parameters
        ----------
        user : str
            Two lower case letter language code.
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')

        try:
            cls = selenium.get_element_attribute(
                "top_menu_user_info_lang_%s_checked" % lang, "class")
        except ValueError:
            current = None
        else:
            current = None if 'selected-language-icon-hidden' in cls else lang

        if current != lang:
            selenium.wait_until_element_is_visible('top_menu_user_dropdown')
            selenium.click_element('top_menu_user_dropdown')
            selenium.wait_until_element_is_visible('top_menu_user_info_lang_%s' % lang)
            selenium.click_element('top_menu_user_info_lang_%s' % lang)
            selenium.add_cookie('used_ui_language', lang, path='/')
            self.wait_until_language_is_saved()

    def wait_until_language_is_saved(self):
        """Make sure that no /api/change-language request is active in the background.

        Because overlapping change-language requests may cause test failures.
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')
        selenium.wait_for_condition('return !window.bolfak_language_save_in_progress')

    def pause(self):
        """Suspend robot test execution and wait until ENTER is pressed.

        This utility keyword is useful for debugging proposes, when you want to stop tests at some
        point in order to inspect what is going on.

        While tests are stopped, you can user browser's developers tools in order to inspect state
        of a page.
        """
        logger.console('\npress enter to continue...')
        input()  # noqa

    def split_string_into_list(self, string_to_split, split_delimiter=','):
        return string_to_split.split(split_delimiter)

    def open_email(self, email_address, position=1, expected_template=None):
        """Opens the Viestikeskus app and searches for the email received on position
        (last email has position 1)

        Parameters:
        :param email_address: the recipient
        :param position: the position of the email being accessed in search results
        :param expected_template: the template used for the email
        """
        selenium = BuiltIn().get_library_instance('SeleniumLibrary')

        PAGE_URL = "https://devel-viestikeskus.tilaajavastuu.fi/"
        SEARCH_PART = "goToSearch"
        MESSAGE_CONTENT_PART = "showMessageContent"

        message_content_main_button = "xpath=(//center//a)[1]"
        recipient_field = "id=receiver"
        search_button = "xpath=//input[@type='submit']"
        sort_message_by_creation_button = "xpath=//a[@href='actionSortMessages?orderBy=created desc']"
        message_template_field = "xpath=//table[@border=1]//tr[<REPLACE>]//td[2]"
        message_sent_date_field = "xpath=//table[@border=1]//tr[<REPLACE>]//td[4]"
        show_message_button = "xpath=(//form[contains(@action, '/actionShowMessage')])[<REPLACE>]"

        selenium.go_to(PAGE_URL + SEARCH_PART)
        selenium.maximize_browser_window()
        selenium.wait_until_page_contains_element(recipient_field)
        selenium.input_text(recipient_field, email_address)
        selenium.click_element(search_button)

        # sorting the emails by created date is needed to pick the correct one
        selenium.wait_until_page_contains_element(sort_message_by_creation_button)
        selenium.click_element(sort_message_by_creation_button)
        time.sleep(1)

        # as a safety precaution, the date of the email sent is checked to be 'today'
        expected_sent_date = datetime.datetime.now().strftime('%d.%m.%Y')
        sent_date_field = self.get_dynamic_locator(message_sent_date_field, str(int(position) + 1))
        actual_sent_date = selenium.get_text(sent_date_field)
        BuiltIn().should_be_equal_as_strings(expected_sent_date,
                                             actual_sent_date,
                                             msg='Unexpected sent date! ({})'.format(
                                                 actual_sent_date))

        if expected_template:
            template_field = self.get_dynamic_locator(message_template_field,
                                                      str(int(position) + 1))
            actual_template = selenium.get_text(template_field)
            BuiltIn().should_start_with(actual_template, expected_template,
                                        msg='Unexpected email template found! ({})'.format(
                                            actual_template),
                                        ignore_case=True)
        latest_email = self.get_dynamic_locator(show_message_button, str(position))
        latest_email_link = selenium.get_element_attribute(latest_email, attribute="action").encode(
            'ascii', 'ignore')

        elements_to_replace = str(PAGE_URL) + str("actionShowMessage?id=")
        selenium.go_to(PAGE_URL + MESSAGE_CONTENT_PART + latest_email_link.decode('ascii').replace(
            elements_to_replace, "?id="))

        selenium.wait_until_page_contains_element(message_content_main_button)

    def get_dynamic_locator(self, locator, *replacements):
        """Replaces elements of <locator> with a series of replacements

        Parameters:
        :param locator: the locator used to identify an element on a page
        :param *replacements: series of elements to replace placeholder with locator
        """
        placeholder = '<REPLACE>'

        for replacement in replacements:
            locator = locator.replace(placeholder, replacement, 1)

        logger.debug('Locator after replace: {}'.format(locator))
        return locator

    def fixture_featureflag_enable(self, featureflag):
        """Enables a specific feature flag
        List of flags used by bolfin (and their default state) can be found in:
        docker/testing_bolfin.envfile and docker/testing_bolfin_ci.envfile

        :param featureflag: The name of the flag to be enabled minus the
                            for ex. to enable feature_flag_disable_emails call fixture_featureflag_enable("disable_emails")
        """
        url = urljoin(BuiltIn().get_variable_value(name='${BASE_URL}'),
                      '/api/testdata/featureflag/%s/enable' % featureflag)
        response = requests.get(url)
        response.raise_for_status()

    def fixture_featureflag_disable(self, featureflag):
        """Disables a specific feature flag
        List of flags used by bolfin (and their default state) can be found in:
        docker/testing_bolfin.envfile and docker/testing_bolfin_ci.envfile

        :param featureflag: The name of the flag to be enabled minus the <feature_flag_> part
                            for ex. to disable feature_flag_disable_emails call fixture_featureflag_enable("disable_emails")
        """
        url = urljoin(BuiltIn().get_variable_value(name='${BASE_URL}'),
                      '/api/testdata/featureflag/%s/disable' % featureflag)
        response = requests.get(url)
        response.raise_for_status()
