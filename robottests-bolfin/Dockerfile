FROM    crvaultitdevtest.azurecr.io/python:3.11-bookworm

COPY    requirements.txt /root/requirements.txt
COPY    vendor /root/vendor

RUN     pip3 install -f /root/vendor -r /root/requirements.txt

RUN     apt-get update && apt-get install -yq \
            firefox-esr && \
        apt-get clean && \
        rm -rf /var/lib/apt/lists/*

RUN     wget -q "https://github.com/mozilla/geckodriver/releases/download/v0.30.0/geckodriver-v0.30.0-linux64.tar.gz" -O /tmp/geckodriver.tgz  \
        && tar zxf /tmp/geckodriver.tgz -C /usr/bin/ \
        && rm /tmp/geckodriver.tgz

COPY    . /robottests
COPY    ./wait-for-qvarn.sh /
