#!/bin/bash

set -o errexit

cd "$(dirname "$0")"

robot_location=$(which robot)

echo "Waiting for BOL..."
wget -q --retry-connrefused -T 30 -O /dev/null http://app:8080/api/status?recursive=no

./robot \
  -P . \
  --outputdir output \
  --debugfile debug.log \
  -e not_ready \
  -n open_issue \
  --listener listeners/listener.py \
  -C ansi \
  --robot-script $robot_location \
  --retry=3 \
  $ROBOT_EXTRA_ARGS \
