# Finnish Bolagsfakta Robot tests

This directory contains Bolagsfakta Robot test cases for BOLFIN.

## Running tests locally

Install Robot Framework

    sudo apt install firefox firefox-geckodriver xserver-xephyr
    make

Update BOL Docker containers to latest versions

    ../docker-compose/bolfin pull
    ../docker-compose/bolfin build --pull

Start BOL Docker containers

    ../docker-compose/bolfin up

Run Robot tests

    ./run-dev-tests.sh --xephyr

Note: "Tests.No Permissions User.Sign-In" will fail because it tries to open the no permissions
page on the local service portal at http://sp, but that only works when you run
robot tests inside Docker.

Example of running Robot tests with Robot command line options (in robottest-bolfin directory)

    ./run-dev-tests.sh --xephyr -i BOL-2000 -e BOL-2001

If you don't want to see the browser window, use

    xvfb-run ./run-dev-tests.sh ...

When done, shut down the Docker containers with

    ../docker-compose/bolfin down

## External requirements

The test suite needs external network resources to be accessible:

- https://auth-azure-alpha.id06.se
- sftp://sftp.alpha.tilaajavastuu.io
- https://devel-laskutuskeskus.tilaajavastuu.fi
- Bisnode

Some of them are reachable only over a VPN.  Which VPN you use also matters:
I've had trouble with the ID06 VPN, but the STV VPN works fine.

## Testing a specific build

You can specify the `IMAGE_TAG` environment variable to a CI pipeline number
before `bolfin build --pull` to test that specific build.  By default
docker-compose will use the last successful master build (tagged 'latest' in
the Docker registry).

## Info on Test data

The test data used in BOLFIN robots is set up differently based on the country a company is registered in. There are 3 groups of countries:
- Finland
- Estonia
- Sweden, Lithuania, Latvia and Poland

Furthermore, there are (potentially) 3 sets of data needed for each company, that might (or might not) come from 3 different places:
- archived reports
- company search data
- company details

#### Estonia:
- company search data + company details:
  - data is taken from `kirjasto > lkreportstatus3` - also under [Company API project](https://git.vaultit.org/FacadeAPI/CompanyAPI/blob/master/classic-mysql/initdb/50-test-data.sql)
- archived reports:
  - archived reports are from `arkisto.freereports` under [Company API project](https://git.vaultit.org/FacadeAPI/CompanyAPI/blob/master/classic-mysql/initdb/50-test-data.sql)                                                                                                                                                                                                                                                                                                                                                                                       
  - for the report to actually show up more things need to happen:
    - company the report belongs to needs to exist (see company search data + company details above)
    - user needs to have an active Report PRO subscription
    - `create_report_access()` from `lib/qvarn_api_client.py` needs to be called with the robots test case (this will create equivalent qvarn resources)
    - order is important too, if `create_report_access()` is called before the Report PRO subscription is activated, the reports will show up as _Unavailable_

#### Finland:
- company search data:
  - comes from bisnode (at least for now)
  - 3rd party system, data can't be manipulated
- company details:
  - comes from atytj / asiakastieto (at least for now)
  - 3rd party system, data cant be manipulated
- archived reports:
  - if company is a RELIABLE PARTNER, archived reports can be inserted under [Company API project](https://git.vaultit.org/FacadeAPI/CompanyAPI/blob/master/classic-mysql/initdb/50-test-data.sql)


Proposal to change 3rd party system: https://jira.tilaajavastuu.fi/browse/BOL-2664

#### Sweden, Lithuania, Latvia and Poland:
- company search data and company details:
  - info is derived from report file names uploaded to: `sftp://sftp.alpha.tilaajavastuu.io`
  - connect via _winscp_ or _filezilla_ (credentials for alpha are [here](https://git.vaultit.org/Foretagsdeklaration/foretagsdeklaration/blob/master/docker-compose/bolfin.yml#L73) PORT is 22 :) )
  - please NOTE that companies / reports uploaded to `sftp://sftp.alpha.tilaajavastuu.io` can ALSO be reached in ALPHA
- NO support for archive reports.

## Adding test data

#### arkisto insert (with explanations):

```sql
INSERT INTO `freereports` VALUES ({freereportid}, '{added_date}',        '{business_id}', '{company_name}',           '{country_code}', '{created_date}',      '{dtd_version}', '{file_name}',             '{latest_source_date}', '{location_path}', '{oldest_source_date}', {report_status}, '{valid_until_date}' );
INSERT INTO `freereports` VALUES (18597071,       '2012-06-11 10:21:03', '0100204-0',     'Brand Factory Finland Oy', 'FI',             '2012-06-11 10:21:03', '2.0',           'FI010020401339406654001', '2012-06-11 10:21:03',  '/2012/06/11',     '2012-06-11 10:21:03',  11,              '2050-01-01 00:00:00');
```
where:
> `{freereportid}` needs to be unique 
>
> `'{dtd_version}'` is always 2.0
> 
> `'{file_name}'` is composed of: `{country_code}` + `{business_id without the - }` + `{archive_code}` (from kirjasto insert); it also needs to match the pdf file name associated to this entry
>
> `'{location_path}'` must match added/created/latest_source/oldest_source/ date (not sure which specifically); associated pdf needs to be in this path [here](https://git.vaultit.org/FacadeAPI/CompanyAPI/tree/master/arkisto-http)
> 
> `{report_status}` represents the status of the report (`10` = OK, `11` = Attention, `12` = Incomplete, `13` = Investigate, `14` = Warning!)

#### kirjasto insert (with explanations):

```sql
INSERT INTO `lkreportstatus3` VALUES ('{business_id}', {latest_report_status}, '{company_name}',           '{country_code}', '{changed_date}',      '{created_date}',      '{archive_code}', '{valid_until_date}',  '{vat}',      {tax_status});
INSERT INTO `lkreportstatus3` VALUES ('0100204-0',     11,                     'Brand Factory Finland Oy', 'FI',             '2019-03-13 13:36:18', '2017-11-03 14:21:31', '1339406654001',  '2050-01-01 00:00:00', 'FI01002040', 0           );
```
where:
> `{latest_report_status}` needs to match latest status from arkisto table
>
> `'{archive_code}'` needs to be unique
>
> `'{vat}'` composed of: `{country_code}` + `{business_id without the - }`
>
> `{tax_status}` not really represented within Report / Report PRO so we always set it to 0 (`0` = ok, `10` = Payment arrangement, `20` = Information pending, `40` = not OK)

### files uploaded to sftp server:

each country (Sweden, Lithuania, Latvia, Poland) has its own folder under `lk_reports`
each file has a format:
```
Swedish Robot firm 4_*********-6666_0171695-9_18022020_10.pdf
```
where:
>   `Swedish Robot firm 4` is the name of the company
> 
>   `*********` is the business ID 
>
>   `0171695-9` is the business ID of the Finnish company this company is a subsidiary of, if it's NOT a subsidiary, this value set to `-`
>
>   `18022020` is the archived date i.e. this report was archived on 18.02.2020
>
>   `10` is the report's status (where `10` = OK)
