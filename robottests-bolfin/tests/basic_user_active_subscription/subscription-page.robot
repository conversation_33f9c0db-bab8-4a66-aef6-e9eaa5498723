*** Settings ***
Resource       ../../resources/generic-resources.robot
Suite Setup    Go to "subscription" page

*** Test Cases ***
Basic user will not be able to terminate Report PRO service
    [Tags]    BOL-2351
    [Setup]    Set Test Variable    ${SUBSCRIPTION_STATE}    active
    Given "bolfin_1_basic" was logged in
    When user visits "subscription" page
    Then page will not contain "terminate" button
    And message about missing permissions will be shown
