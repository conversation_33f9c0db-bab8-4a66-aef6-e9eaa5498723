*** Settings ***
Resource       ../../resources/generic-resources.robot
Suite Setup    Go to "subscription" page

*** Test Cases ***
Basic user will not be able to subscribe Report PRO service
    [Tags]    BOL-2229    BOL-2351
    [Setup]    Set Test Variable    ${SUBSCRIPTION_STATE}    not-active
    Given "bolfin_1_basic" was logged in
    When user visits "subscription" page
    Then page will not contain "subscribe" button
    And message about missing permissions will be shown
