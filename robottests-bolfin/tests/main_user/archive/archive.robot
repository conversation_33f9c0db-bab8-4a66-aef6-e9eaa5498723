*** Settings ***
Resource          ../../../resources/generic-resources.robot
Test Teardown     Archive test teardown
Force Tags        BOL-2006

*** Test Cases ***
Report downloaded before subscribing - Report status and report will not be available
    [Setup]    Company view case setup    name=Estonian Robot Company 1    id=12186955    status=OK    country=EST
    Given report was downloaded
    And Report PRO was subscribed
    When user opens company view in Search history page
    Then in archive pane report status and report will not be available
    But archive ID and download date will be shown
    And description will inform about reports downloaded before current subscription

Report downloaded after subscribing - Report status and report will be available
    [Setup]    Company view case setup    name=Estonian Robot Company 2    id=45820572    status=Attention    country=EST
    Given Report PRO was subscribed
    And report was downloaded
    When user opens company view in Search history page
    Then in archive pane report status and report will be available
    And archive ID and download date will be shown

Report downloaded before terminating subscription - Report status and report will not be available
    [Tags]    BOL-1895
    [Setup]    Company view case setup    name=Estonian Robot Firm 1    id=63764727    status=Incomplete    country=EST
    Given Report PRO was subscribed
    And report was downloaded
    And subscription was terminated
    When user opens company view in Search history page
    Then in archive pane report status and report will not be available
    But archive ID and download date will be shown
    And description will inform about terminated licence

Service re-subscribed - Report status and report will be available only reports downloaded after re-subscription
    [Setup]    Company view case setup    name=Estonian Robot Firm 2    id=57636873    status=Investigate    country=EST
    Given report was downloaded during Report PRO subscription
    And report was downloaded without Report PRO subscription
    And report was downloaded during Report PRO subscription
    When user opens company view in Search history page
    Then only reports downloaded after re-subscription will be available

Report will open in new window from archived list
    [Setup]    FI company view case setup    name=Testiyritys 'Yy' Oy    id=2327327-1    status=OK
    Given archived reports were listed in archived reports pane
    When user clicks report icon in archived list
    Then archived report will be opened in new window

Tooltip will be shown for hidden report
    [Tags]    BOL-2240
    [Setup]    Hidden report tooltip case setup
    ...        name=Testiyritys Bff Oy    id=0100204-0    status=Attention    element=hidden-report
    ...        message=This report is unavailable because it was fetched before your current subscription began
    Given hidden report was listed in archived reports
    When mouse is hovered on hidden report
    Then tooltip will be shown

Unknown company with archived report - View latest report button will not be shown
    [Tags]    BOL-2383
    [Setup]    Unknown company archived report case setup    archive_id=FI111111111339406654121    country_iso=FI    company_id=1111111-1
    Given report was archived for company
    When user opens company view in Search history page
    Then View latest report button will not be shown

Archived report for unknown company - Company will be listed
    [Tags]    BOL-2383
    [Setup]    set multiple test variables    ARCHIVE_ID:FI111111111339406654121,COUNTRY_ISO:FI,COMPANY_ID:1111111-1
    Given report was archived for company
    When user visits "search history" page
    Then company will be listed as unknown

Check archived report details with every status
    [Tags]      BOL-2379
    [Setup]     Set up variables for multiple archived reports
    ...         name=Estonian Robot Firm 3
    ...         id=23572879
    ...         archive_id_part1=EE23572879133940665
    ...         country=EST
    ...         country_iso=EE
    Given Report PRO was subscribed
    And multiple reports were archived with archive_ids: "@{ARCHIVE_ID_LIST}"
    When company view on Search history page was open
    Then there should be "5" archived report(s) with archive id(s): "@{ARCHIVE_ID_LIST}" and status(es): "@{STATUS_LIST}"

Check archived report details for Terminated company
    [Tags]      BOL-2441
    [Setup]     Set up Terminated company variables
    ...         company_name=NN-Moto
    ...         id=2623461-4
    ...         country=FIN
    ...         country_iso=FI
    ...         archive_id=FI262346141339406650012
    ...         status=Attention
    Given report was archived for company
    When user opens company view in Search history page
    Then user sees selected company has status "Terminated"
    And there should be "1" archived report(s) with archive id(s): "@{ARCHIVE_ID_LIST}" and status(es): "@{STATUS_LIST}"
