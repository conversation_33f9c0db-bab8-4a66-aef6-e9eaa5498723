*** Settings ***
Resource          ../../../resources/generic-resources.robot
Suite Teardown    delete report accesses    EE
Test Teardown     close report windows and company view
Force Tags        BOL-2001

*** Variables ***
${SEARCH_TEXT}=     Estonian Robot

*** Test Cases ***
OK status - Selecting company will show company and report information
    [Setup]    Company view case setup    name=Estonian Robot Company 1    id=12186955    status=OK    country=EST
    Given search was performed for "Estonian" companies
    When user selects company
    Then company and reliable partner information will be shown

Attention status - Selecting company will show company and report information
    [Setup]    Company view case setup    name=Estonian Robot Company 2    id=45820572    status=Attention    country=EST
    Given search was performed for "Estonian" companies
    When user selects company
    Then company and reliable partner information will be shown

Investigate status - Selecting company will show company and report information
    [Setup]    Company view case setup    name=Estonian Robot Firm 2    id=57636873   status=Investigate    country=EST
    Given search was performed for "Estonian" companies
    When user selects company
    Then company and reliable partner information will be shown

Incomplete status - Selecting company will show company and report information
    [Setup]    Company view case setup    name=Estonian Robot Firm 1    id=63764727   status=Incomplete    country=EST
    Given search was performed for "Estonian" companies
    When user selects company
    Then company and reliable partner information will be shown

Warning status - Selecting company will show company and report information
    [Setup]    Company view case setup    name=Estonian Robot Firm 3    id=23572879   status=Warning!    country=EST
    Given search was performed for "Estonian" companies
    When user selects company
    Then company and reliable partner information will be shown

View latest report button will open report in new window
    [Setup]    Company view case setup    name=Estonian Robot Firm 3    id=23572879   status=Warning!    country=EST
    Given company view on Search page was open
    When user clicks View latest report button dropdown
    Then report will be opened in new window

Report cannot be achived message will not be shown in company view
    [Setup]    Company view case setup    name=Estonian Robot Firm 2    id=57636873   status=Investigate    country=EST
    Given search was performed for "Estonian" companies
    When user selects company
    Then company view will not show report cannot be archived message
