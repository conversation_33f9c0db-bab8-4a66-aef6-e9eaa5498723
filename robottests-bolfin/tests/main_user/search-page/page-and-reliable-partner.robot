*** Settings ***
Resource      ../../../resources/generic-resources.robot

*** Test Cases ***
Reliable Partner logo will be shown for reliable partner company
    [Tags]    BOL-2116
    [Setup]    Search case setup    2327327-1    Finnish
    Given user was on "search" page
    When user searches company which is Reliable Partner
    Then Reliable Partner logo will be shown for the company

Reliable Partner logo will not be shown for non reliable partner company
    [Tags]    BOL-2116
    [Setup]    Search case setup    0116659-8    Finnish
    Given user was on "search" page
    When user searches company which is non Reliable Partner
    Then Reliable Partner logo will not be shown for the company

No Report PRO subscription - Message will be shown about upgrading to Report PRO
    [Tags]    BOL-2117
    Given Report PRO was not subscribed
    When user visits "search" page
    Then "You are currently using the free version of the Report service" warning message will be shown

Report PRO subscription active - Message will be shown about Report PRO being active
    [Tags]    BOL-2215
    Given Report PRO was subscribed
    When user visits "search" page
    Then "The Report PRO service is in use" message will be shown
    [Teardown]    set subscription canceled    go_after_page=search

Reliable Partner check mark will be shown in company list when company view is open for Reliable Partner
    [Tags]    BOL-2218
    [Setup]    Search case setup    2327327-1    Finnish
    Given company list contains Reliable Partner company
    When user opens company view in Search page
    Then Reliable Partner check mark will be in company list

Reliable Partner check mark will not be shown in company list when company view is open for non Reliable Partner
    [Tags]    BOL-2218
    [Setup]    Search case setup    0116659-8    Finnish
    Given company list contains non Reliable Partner company
    When user opens company view in Search page
    Then Reliable Partner check mark will not be in company list

Tooltip for Reliable Partner check mark header will show explanation
    [Tags]    BOL-2218
    [Setup]    set multiple test variables
    ...        SEARCH_TEXT:2327327-1,COUNTRY_UN:FIN,COMPANY:2327327-1,MESSAGE:Reliable Partner,ELEMENT:RP-header
    Given company view on Search page was open
    When mouse is hovered on RP header
    Then tooltip will be shown
