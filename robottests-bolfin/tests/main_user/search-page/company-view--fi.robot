*** Settings ***
Resource          ../../../resources/generic-resources.robot
Suite Teardown    delete report accesses    FI
Test Teardown     close report windows and company view

*** Variables ***
${COUNTRY_UN}=    FIN

*** Test Cases ***
OK status - Selecting company will show company and report information
    [Tags]    BOL-2001
    [Setup]    FI company view case setup    name=Testiyritys 'Yy' Oy    id=2327327-1    status=OK
    Given search was performed for "Finnish" companies
    When user selects company
    Then company and reliable partner information will be shown

Attention status - Selecting company will show company and report information
    [Tags]    BOL-2001
    [Setup]    FI Company view case setup    name=Testiyritys Bff Oy    id=0100204-0   status=Attention
    Given search was performed for "Finnish" companies
    When user selects company
    Then company and reliable partner information will be shown

Investigate status - Selecting company will show company and report information
    [Tags]    BOL-2001
    [Setup]    FI Company view case setup    name=Testiyritys Sks Oy    id=0102282-6    status=Investigate
    Given search was performed for "Finnish" companies
    When user selects company
    Then company and reliable partner information will be shown

Incomplete status - Selecting company will show company and report information
    [Tags]    BOL-2001
    [Setup]    FI Company view case setup    name=Testiyritys Efgh Oy    id=0100346-5  status=Incomplete
    Given search was performed for "Finnish" companies
    When user selects company
    Then company and reliable partner information will be shown

Warning status - Selecting company will show company and report information
    [Tags]    BOL-2001
    [Setup]    FI Company view case setup    name=Testiyritys Abcd Oy    id=0103189-6    status=Warning!
    Given search was performed for "Finnish" companies
    When user selects company
    Then company and reliable partner information will be shown

View latest report button will open report in new window
    [Tags]    BOL-2001
    [Setup]    FI Company view case setup    name=Testiyritys Bff Oy    id=0100204-0   status=Attention
    Given company view on Search page was open
    When user clicks View latest report button dropdown
    Then report will be opened in new window

Report cannot be achived message will not be shown in company view
    [Tags]    BOL-2001
    [Setup]    FI Company view case setup    name=Testiyritys Sks Oy    id=0102282-6    status=Investigate
    Given search was performed for "Finnish" companies
    When user selects company
    Then company view will not show report cannot be archived message

Zeckit widget will be shown in company view
    [Tags]    BOL-2223    open_issue    # set as non critical with open_issue as Zeckit service tends to have issues
    [Setup]    FI company view case setup    name=Testiyritys 'Yy' Oy    id=2327327-1    status=OK
    Given search was performed for "Finnish" companies
    When user selects company
    Then Zeckit widget will be shown
    [Teardown]    Unselect Frame
