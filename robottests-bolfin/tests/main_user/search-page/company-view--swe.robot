*** Settings ***
Resource         ../../../resources/generic-resources.robot
Test Teardown    close report windows and company view

*** Variables ***
${SEARCH_TEXT}=     Swedish Robot

*** Test Cases ***
OK status - Selecting company will show company and report information
    [Tags]    BOL-2001
    [Setup]    Company view case setup    name=Swedish Robot firm 2    id=*********-4444    status=OK    country=SWE
    Given search was performed for "Swedish" companies
    When user selects company
    Then company and reliable partner information will be shown

Attention status - Selecting company will show company and report information
    [Tags]    BOL-2001
    [Setup]    Company view case setup    name=Swedish Robot company 1    id=*********-1111    status=Attention    country=SWE
    Given search was performed for "Swedish" companies
    When user selects company
    Then company and reliable partner information will be shown

Investigate status - Selecting company will show company and report information
    [Tags]    BOL-2001
    [Setup]    Company view case setup    name=Swedish Robot company 2    id=*********-2222    status=Investigate    country=SWE
    Given search was performed for "Swedish" companies
    When user selects company
    Then company and reliable partner information will be shown

Incomplete status - Selecting company will show company and report information
    [Tags]    BOL-2001
    [Setup]    Company view case setup    name=Swedish Robot firm 1    id=*********-3333   status=Incomplete    country=SWE
    Given search was performed for "Swedish" companies
    When user selects company
    Then company and reliable partner information will be shown

Warning status - Selecting company will show company and report information
    [Tags]    BOL-2001
    [Setup]    Company view case setup    name=Swedish Robot firm 3    id=*********-5555    status=Warning!    country=SWE
    Given search was performed for "Swedish" companies
    When user selects company
    Then company and reliable partner information will be shown

View latest report button will open report in new window
    [Tags]    BOL-2001
    [Setup]    Company view case setup    name=Swedish Robot firm 1    id=*********-3333   status=Incomplete    country=SWE
    Given company view on Search page was open
    When user clicks View latest report button
    Then report will be opened in new window

Report cannot be achived message will be shown in company view
    [Tags]    BOL-2001
    [Setup]    Company view case setup    name=Swedish Robot firm 3    id=*********-5555    status=Warning!    country=SWE
    Given search was performed for "Swedish" companies
    When user selects company
    Then company view will show report cannot be archived message

Company with Finnish business ID - Finnish business ID will be shown in company view
    [Tags]    BOL-2122
    [Setup]    Company view case setup    name=Swedish Robot company 3    id=*********-6666   status=Investigate    country=SWE
    Given search was performed for "Swedish" companies
    When user selects company
    Then company view will show "8357135-5" for Finnish business ID

Report update date will be shown in company view
    [Tags]    BOL-2122
    [Setup]    Company view case setup    name=Swedish Robot firm 3    id=*********-5555    status=Warning!    country=SWE
    Given search was performed for "Swedish" companies
    When user selects company
    Then company view will show "01/18/2019" for report update date

Report PRO subscribed - Swedish company will not be added to Search history
    [Tags]    BOL-2162
    [Setup]    Company view case setup    name=Swedish Robot firm 2    id=*********-4444    status=OK    country=SWE
    Given Report PRO was subscribed
    And company view on Search page was open
    When user clicks View latest report button
    Then company will not be listed in Search history
    close report windows and company view
    [Teardown]    Terminate subscription and go to "search" page
