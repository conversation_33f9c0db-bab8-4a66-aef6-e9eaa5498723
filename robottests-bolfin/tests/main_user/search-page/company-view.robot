*** Settings ***
Resource         ../../../resources/generic-resources.robot
Test Setup       Company view case setup    name=Lithuanian Robot firm 2    id=4400-4444    status=OK    country=LTU
Test Teardown    close report windows and company view
Force Tags       BOL-2001

*** Variables ***
${SEARCH_TEXT}=     Lithuanian Robot

*** Test Cases ***
Selected company will be highlighted in company list
    Given search was performed for "Lithuanian" companies
    And no company is selected
    When user selects company
    Then company row will be highlighted

Company view will be closed with Close view button
    Given company view on Search page was open
    When user clicks Close view button
    Then company view will be closed

Company view will be closed with Esc key
    Given company view on Search page was open
    When user presses Esc key
    Then company view will be closed
