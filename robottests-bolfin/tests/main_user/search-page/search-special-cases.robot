*** Settings ***
Resource      ../../../resources/generic-resources.robot

*** Test Cases ***
Clearing filter will reset search
    [Tags]    BOL-2213
    [Setup]    set multiple test variables    SEARCH_TEXT:ompany,COUNTRY_UN:SWE,COMPANY:*********-6666
    Given search was performed for "Swedish" companies
    And company view on Search page was open
    When clear filter button is clicked
    Then search text will be cleared
    And company list will be cleared
    And company view will be closed

One character search string - Error message will be shown
    [Tags]    BOL-2115
    Given user was on "search" page
    When user performs search with "a"
    Then search error "Please enter at least two characters" will be shown

Search error message is shown - Performing valid search will remove error message
    [Tags]    BOL-2115
    Given search error message was shown
    When user performs search with "aa"
    Then search error message will be removed
    [Teardown]    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]

Changing country will trigger new search
    [Tags]    BOL-2236
    [Setup]    set multiple test variables    SEARCH_TEXT:ompany,COUNTRY_UN:SWE,COMPANY:*********-6666
    Given search was performed for "Swedish" companies
    And company view on Search page was open
    When user changes search to "Lithuanian" companies
    Then "Lithuanian" companies will be listed
    And company view will be closed

Search string not set - Changing country will not trigger new search
    [Tags]    BOL-2236
    [Setup]    set multiple test variables    SEARCH_TEXT:ompany,COUNTRY_UN:SWE,COMPANY:*********-6666
    Given search was performed for "Swedish" companies
    And search text box was empty
    When user changes search to "Lithuanian" companies
    Then "Swedish" companies will be listed

One character search string - Changing country will show error message
    [Tags]    BOL-2236    BOL-2315
    [Setup]    set multiple test variables    SEARCH_TEXT:ompany,COUNTRY_UN:SWE,COMPANY:*********-6666
    Given search was performed for "Swedish" companies
    And search text was "a"
    When user changes search to "Lithuanian" companies
    Then search error "Please enter at least two characters" will be shown
    And "Swedish" companies will be listed

Vague search string - Warning about too many results will be shown
    [Tags]    BOL-2230
    [Setup]    set multiple test variables    SEARCH_TEXT:Oy,COUNTRY_UN:FIN
    Given user was on "search" page
    When user searches "Finnish" companies
    Then warning message containing "Too many results" will be shown
    [Teardown]    Vague search case teardown

Too many results warning shown - New search will remove the warning
    [Tags]    BOL-2230
    [Setup]    set multiple test variables    SEARCH_TEXT:'Yy' Oy,COUNTRY_UN:FIN
    Given too many results warning was shown
    When user searches "Finnish" companies
    Then warning will be removed
    [Teardown]    Vague search case teardown
