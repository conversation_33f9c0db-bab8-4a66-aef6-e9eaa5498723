*** Settings ***
Resource      ../../../resources/generic-resources.robot
Force Tags    BOL-2000

*** Test Cases ***
Text - Search for Finnish companies will list found companies
    [Tags]    BOL-2050
    [Setup]    Set Test Variable    ${SEARCH_TEXT}    jkl
    Given user was on "search" page
    When user searches "Finnish" companies
    Then "Finnish" companies will be listed

Company ID - Search for Finnish companies will list found companies
    [Setup]    Set Test Variable    ${SEARCH_TEXT}    2327327-1
    Given user was on "search" page
    When user searches "Finnish" companies
    Then "Finnish" companies will be listed

Text - Search for Estonian companies will list found companies
    [Tags]    BOL-2050
    [Setup]    Set Test Variable    ${SEARCH_TEXT}    ompany
    Given user was on "search" page
    When user searches "Estonian" companies
    Then "Estonian" companies will be listed

Company ID - Search for Estonian companies will list found companies
    [Setup]    Set Test Variable    ${SEARCH_TEXT}    12186955
    Given user was on "search" page
    When user searches "Estonian" companies
    Then "Estonian" companies will be listed

Text - Search for Swedish companies will list found companies
    [Tags]    BOL-2050
    [Setup]    Set Test Variable    ${SEARCH_TEXT}    ompany
    Given user was on "search" page
    When user searches "Swedish" companies
    Then "Swedish" companies will be listed

Company ID - Search for Swedish companies will list found companies
    [Setup]    Set Test Variable    ${SEARCH_TEXT}    *********-1111
    Given user was on "search" page
    When user searches "Swedish" companies
    Then "Swedish" companies will be listed

Text - Search for Lithuanian companies will list found companies
    [Tags]    BOL-2050
    [Setup]    Set Test Variable    ${SEARCH_TEXT}    ompany
    Given user was on "search" page
    When user searches "Lithuanian" companies
    Then "Lithuanian" companies will be listed

Company ID - Search for Lithuanian companies will list found companies
    [Setup]    Set Test Variable    ${SEARCH_TEXT}    2200-2222
    Given user was on "search" page
    When user searches "Lithuanian" companies
    Then "Lithuanian" companies will be listed

Clearing filter will reset search
    [Tags]    BOL-2213
    [Setup]    set multiple test variables    SEARCH_TEXT:ompany,COUNTRY_UN:SWE,COMPANY:*********-6666
    Given search was performed for "Swedish" companies
    And company view on Search page was open
    When clear filter button is clicked
    Then search text will be cleared
    And company list will be cleared
    And company view will be closed

One character search string - Error message will be shown
    [Tags]    BOL-2115
    Given user was on "search" page
    When user performs search with "a"
    Then search error "Please enter at least two characters" will be shown

Search error message is shown - Performing valid search will remove error message
    [Tags]    BOL-2115
    Given search error message was shown
    When user performs search with "aa"
    Then search error message will be removed
