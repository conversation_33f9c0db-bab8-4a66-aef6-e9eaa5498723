*** Settings ***
Resource      ../../../resources/generic-resources.robot

Default Tags    BOL-2851

*** Test Cases ***
Search for subsidiary by Finnish secodnary business id (0100516-1)
    [Setup]     Company and Parent setup
    ...         name=Testiyritys Ftap Oy    id=0100516-1    status=NOT_USED      country=FIN
    ...         parent_name=AL ALTEGO SP. Z O.O.   parent_id=0000272790   parent_status=Incomplete      parent_country=POL
    Given search was performed for "Finnish" companies
    And search text was "0100516-1"
    And Reliable Partner logo will be shown for the company
    When user selects company
    And user visits parent company
    Then parent company and reliable partner information will be shown
    And Reliable Partner check mark will be in company list

Search for Finnish subsidiary by name (Fototapio)
    [Setup]     Company and Parent setup
    ...         name=Testiyritys Ftap Oy    id=0100516-1    status=NOT_USED      country=FIN
    ...         parent_name=AL ALTEGO SP. Z O.O.   parent_id=0000272790   parent_status=Incomplete      parent_country=POL
    Given search was performed for "Finnish" companies
    And search text was "Testiyritys Ftap Oy"
    And Reliable Partner logo will be shown for the company
    When user selects company
    And user visits parent company
    Then parent company and reliable partner information will be shown
    And Reliable Partner check mark will be in company list

Search for Finnish business id in NON-Finnish country, expect no results (0100516-1)
    [Setup]     Company and Parent setup
    ...         name=Testiyritys Ftap Oy    id=0100516-1    status=NOT_USED      country=FIN
    ...         parent_name=AL ALTEGO SP. Z O.O.   parent_id=0000272790   parent_status=Incomplete      parent_country=POL
    Given clear filter button is clicked
    And search text was "0100516-1"
    When user searches "Polish" companies
    Then verify company list is empty

Search for subsidiary with two parents, expect error (0132814-7)
    [Setup]     FI Company view case setup
    ...         name=Testiyritys Stkn Oy    id=0132814-7    status=NOT_USED
    Given search was performed for "Finnish" companies
    And search text was "0132814-7"
    And Reliable Partner logo will be shown for the company
    When user selects company
    Then user sees error and no option to visit parent company
    And Reliable Partner check mark will be in company list
