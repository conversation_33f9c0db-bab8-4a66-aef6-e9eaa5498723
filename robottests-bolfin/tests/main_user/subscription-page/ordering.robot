*** Settings ***
Resource       ../../../resources/generic-resources.robot
Force Tags     BOL-2004

*** Test Cases ***
Without order reference - Report PRO service can be subscribed
    [Setup]         fixture featureflag disable          disable_emails
    Given sp language was selected as "en"
    And Go to "subscription" page
    When user accepts terms of use
    And user clicks subscribe button
    Then Terminate subscription page will be loaded
    And last email recieved by "${USERNAME}" has template: "vastuugroup_bol_subscription_confirmation_email" and contains: Thank you for subscribing to the Report PRO service!
    [Teardown]      Run Keywords        Terminate subscription and delete subscription data from Qvarn
                    ...                 AND
                    ...                 fixture featureflag enable          disable_emails

With order reference - Report PRO service can be subscribed
    [Setup]         fixture featureflag disable          disable_emails
    Given sp language was selected as "en"
    And Go to "subscription" page
    When user inserts order reference
    And user accepts terms of use
    And user clicks subscribe button
    Then terminate subscription page will be loaded
    And last email recieved by "${USERNAME}" has template: "vastuugroup_bol_subscription_confirmation_email" and contains: Thank you for subscribing to the Report PRO service!
    And order reference will be stored to Qvarn
    [Teardown]      Run Keywords        Terminate subscription and delete subscription data from Qvarn
                    ...                 AND
                    ...                 fixture featureflag enable          disable_emails

Subscribe button will be disabled until TOS checkbox is checked
    Given user was on "subscription" page
    And subscribe button was disabled
    When user accepts terms of use
    Then subscribe button will be enabled
    [Teardown]    set checkbox unchecked   tos-checkbox

Subscribe button will show instructions in tooltip when button is disabled
    [Setup]    set multiple test variables
    ...        MESSAGE:You have to accept the terms of use first,ELEMENT:subscribe-button
    Given user was on "subscription" page
    And subscribe button was disabled
    When mouse is hovered on subscription button
    Then tooltip will be shown

Price for Report PRO service will be 130 euros
    Given user was on "subscription" page
    And price for Report PRO service was stated to be "130" euros
    When user accepts terms of use
    And user clicks subscribe button
    Then price "130" will be stored to Qvarn
    [Teardown]    Terminate subscription and delete subscription data from Qvarn

Warning message for archiving country limits will be shown
    Given user was on "subscription" page
    Then warning message "Only reports of Finnish and Estonian companies can be archived." will be shown

Hide service description button will hide description
    [Setup]    Reset subscription    go_after_page=subscription
    Given user was on "subscription" page
    And service description was shown
    When user clicks "hide" service description button
    Then service description will be hidden

Show service description button will show description
    Given user was on "subscription" page
    And service description was hidden
    When user clicks "show" service description button
    Then service description will be shown
