*** Settings ***
Resource         ../../../resources/generic-resources.robot
Test Teardown    Terminate subscription and delete subscription data from Qvarn
Force Tags       BOL-2005

*** Test Cases ***
Checking terminate checkbox will show warning
    Given Report PRO was subscribed
    And terminate checkbox was unchecked
    When user checks terminate checkbox
    Then terminating warning and acknowledgement checkbox will be shown
    And terminate button will stay disabled

Unchecking terminate checkbox will hide warning
    Given Report PRO was subscribed
    And terminate checkbox was checked
    When user unchecks terminate checkbox
    Then terminating warning and acknowledgement checkbox will be hidden
    And terminate button will stay disabled

Checking warning checkbox will enable terminate button
    [Tags]    BOL-2211
    Given Report PRO was subscribed
    And terminate checkbox was checked
    When user checks warning checkbox
    Then terminate button will be enabled

Unchecking warning checkbox will disable terminate button
    [Tags]    BOL-2211
    Given Report PRO was subscribed
    And terminate checkbox was checked
    And warning checkbox was checked
    And terminate button was enabled
    When user unchecks warning checkbox
    Then terminate button will be disabled

Terminate button will terminate subscription
    [Tags]    BOL-2211
    Given Report PRO was subscribed
    And terminate checkbox was checked
    And warning checkbox was checked
    And terminate button was enabled
    When user clicks terminate button
    Then subscribe page will be loaded

Unchecking terminate checkbox will set warning checkbox unchecked
    Given Report PRO was subscribed
    And terminate checkbox was checked
    And warning checkbox was checked
    When user unchecks terminate checkbox
    Then warning checkbox will be unchecked

Tooltip for disabled terminate subscription button will instruct to check checkboxes
    [Tags]    BOL-2212
    [Setup]    set multiple test variables
    ...        MESSAGE:You have to confirm the termination first,ELEMENT:terminate-button
    Given Report PRO was subscribed
    And terminate subscription button was disabled
    When mouse is hovered on terminate subscription button
    Then tooltip will be shown
