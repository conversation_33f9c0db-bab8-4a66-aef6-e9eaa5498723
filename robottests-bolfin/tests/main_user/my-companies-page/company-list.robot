*** Settings ***
Resource      ../../../resources/generic-resources.robot

*** Variables ***
${COUNTRY_UN}=    FIN

*** Test Cases ***
Latest report will be opened in company list
    [Tags]    BOL-2002  BOL-2002.1
    [Setup]    Set Test Variable    ${COMPANY_ID}    2327327-1
               Set Test Variable    ${LANGUAGE}        FI
    Given user was on "search history" page
    And companies were listed in company list
    When user clicks report icon for company
    Then report will be opened in new window
    [Teardown]    close report windows and company view

Companies will be filtered based on search text
    [Tags]    BOL-2002  BOL-2002.2
    Given user was on "search history" page
    And companies were listed in company list
    When user searches "Firm"
    Then company list will be filtered with "Firm" in company name

Companies will be filtered based on registration id
    [Tags]    BOL-2002  BOL-2002.3
    Given user was on "search history" page
    And companies were listed in company list
    When user searches "2327327-1"
    Then company list will be filtered with "2327327-1" registration id

Clearing filter will reset search
    [Tags]    BOL-2002  BOL-2002.4
    Given filtering was performed for "2327327-1" companies
    When clear filter button is clicked
    Then search text will be cleared
    And company list will show all companies

Loading Search history page with 20 companies listed will loaded in 3 seconds
    [Tags]    BOL-2249
    [Setup]    Search history load case setup
    Given user was on "search" page
    When user visits "search history" page
    Then company list will be loaded in "3" seconds

No Report PRO subscription - Message will be shown about upgrading to Report PRO
    [Tags]    BOL-2191
    Given Report PRO was not subscribed
    When user visits "search history" page
    Then "Only the search history of your company is shown below." message will be shown

Report PRO subscription active - Message will be shown about Report PRO being active
    [Tags]    BOL-2191
    Given Report PRO was subscribed
    When user visits "search history" page
    Then "The Report PRO service is in use." message will be shown
    [Teardown]    Reset subscription    go_after_page=search history
