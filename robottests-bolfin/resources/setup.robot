*** Keywords ***
Basic user suite setup with active subscription
    [Arguments]    ${main_user}    ${basic_user}
    User suite setup    ${main_user}
    Subscribe Report PRO
    Close All Browsers
    User suite setup    ${basic_user}

Browser setup
    Open Browser            ${BASE_URL}         ${BROWSER}
    Set Global Variable     ${WINDOW_WIDTH}     1600
    Set Global Variable     ${WINDOW_HEIGHT}    1050
    Set Window Size         ${WINDOW_WIDTH}     ${WINDOW_HEIGHT}

Select language
    [Arguments]     ${lang}
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Click Element       &{TEMPLATE_ELEMENT}[user-menu]
    Wait Until Element is Visible       &{TEMPLATE_ELEMENT}[language-${lang}]
    Click Element       &{TEMPLATE_ELEMENT}[language-${lang}]

Company view case setup
    [Arguments]    ${name}    ${id}    ${status}    ${country}
    Set Test Variable    ${COMPANY}          ${name}
    Set Test Variable    ${COMPANY_ID}       ${id}
    Set Test Variable    ${REPORT_STATUS}    ${status}
    Set Test Variable    ${COUNTRY_UN}     ${country}
    Set Test Variable    ${LANGUAGE}       FI
    Select Language      en

FI Company view case setup
    [Arguments]    ${name}    ${id}    ${status}
    Set Test Variable    ${SEARCH_TEXT}          ${id}
    Company view case setup    ${name}    ${id}    ${status}    FIN

Hidden report tooltip case setup
    [Arguments]    ${name}    ${id}    ${status}    ${message}    ${element}
    FI Company view case setup    ${name}    ${id}    ${status}
    Set Test Variable    ${MESSAGE}    ${message}
    Set Test Variable    ${ELEMENT}    ${element}

Search history load case setup
    Go to "search" page
    Set Suite Variable    ${SEARCH_TEXT}    Performance
    Search was performed for "Estonian" companies
    Select language  fi
    open reports for search history load test     config_file=${QVARN_DATA}
    Select language  en

Search history suite setup
    Go to "search" page
    Set Suite Variable    ${SEARCH_TEXT}    2327327-1
    Search was performed for "Finnish" companies
    Select language     fi
    open reports    &{USERS}[${USERNAME}]    FIN
    Select language     en
    Set Suite Variable    ${SEARCH_TEXT}    Robot
    Search was performed for "Estonian" companies
    Select language     fi
    open reports    &{USERS}[${USERNAME}]    EST
    Go to "search history" page
    Select language     en

No permissions user suite setup
    [Arguments]    ${username}
    Set Global Variable    ${USERNAME}    ${username}
    Browser setup
    ${user_data}=    Set Variable    &{USERS}[${USERNAME}]
    ${password}=    Set Variable    &{user_data}[password]

Reset subscription
    [Arguments]    ${go_after_page}
    delete subscriptions    &{USERS}[${USERNAME}]
    Close All Browsers
    Browser setup
    Log in
    Go to "${go_after_page}" page

Search case setup
    [Arguments]    ${search_text}    ${country}
    Set Test Variable    ${SEARCH_TEXT}    ${search_text}
    Set Test Variable    ${COUNTRY}    ${country}

Setup user data to Qvarn
    ${users}=    setup user data     config_file=${QVARN_DATA}
    Set Global Variable    ${USERS}    ${users}
    create services    config_file=${QVARN_DATA}

Unknown company archived report case setup
    [Arguments]    ${archive_id}    ${country_iso}    ${company_id}
    Set Test Variable    ${ARCHIVE_ID}      ${archive_id}
    Set Test Variable    ${COUNTRY_ISO}     ${country_iso}
    Set Test Variable    ${COMPANY_ID}      ${company_id}
    Set Test Variable    ${COMPANY}         Unknown
    Go to "search history" page

User suite setup
    [Arguments]    ${username}
    Set Global Variable                     ${USERNAME}    ${username}
    Browser setup
    Log in
    ensure language                         en

Company and Parent setup
    [Arguments]         ${name}    ${id}    ${status}    ${country}     ${parent_name}      ${parent_id}    ${parent_status}    ${parent_country}
    Company view case setup     ${name}    ${id}    ${status}    ${country}
    Set Test Variable    ${SEARCH_TEXT}             ${id}
    Set Test Variable   ${PARENT_COMPANY}          ${parent_name}
    Set Test Variable   ${PARENT_COMPANY_ID}       ${parent_id}
    Set Test Variable   ${PARENT_REPORT_STATUS}    ${parent_status}
    Set Test Variable   ${PARENT_COUNTRY_UN}       ${parent_country}

Set up variables for multiple archived reports
    [Arguments]    ${name}    ${id}     ${archive_id_part1}     ${country}      ${country_iso}
    Set Test Variable       ${COMPANY}              ${name}
    Set Test Variable       ${COMPANY_ID}           ${id}
    Set Test Variable       ${COUNTRY}              ${country}
    Set Test Variable       ${ARCHIVE_ID_PART1}     ${archive_id_part1}
    Set Test Variable       ${COUNTRY_ISO}          ${country_iso}

    ${ARCHIVE_ID_LIST}=     Create List
    ...                     ${ARCHIVE_ID_PART1}4006
    ...                     ${ARCHIVE_ID_PART1}0007
    ...                     ${ARCHIVE_ID_PART1}0008
    ...                     ${ARCHIVE_ID_PART1}0009
    ...                     ${ARCHIVE_ID_PART1}0010
    Set Test Variable       ${ARCHIVE_ID_LIST}
    ${STATUS_LIST}=         Create List
    ...                     Warning!
    ...                     OK
    ...                     Attention
    ...                     Incomplete
    ...                     Investigate
    Set Test Variable       ${STATUS_LIST}

Admin user visits company "${id}" belonging to user "${username}"
    Go To                                           ${SP_BASE_URL}/#/${USERS}[${username}][org_id]/dashboard
    Wait Until Page Contains                        Welcome to your company account         timeout=30

    Go To                                           ${BASE_URL}/#/login/${USERS}[${username}][org_id]
    Wait Until Page Does Not Contain Element        &{TEMPLATE_ELEMENT}[loading-spinner]

    ensure language                                 en
    Go to "subscription" page

Set up Terminated company variables
    [Arguments]    ${company_name}    ${id}     ${country}      ${country_iso}      ${archive_id}       ${status}
    Set Test Variable       ${COMPANY}              ${company_name}
    Set Test Variable       ${COMPANY_ID}           ${id}
    Set Test Variable       ${COUNTRY}              ${country}
    Set Test Variable       ${COUNTRY_ISO}          ${country_iso}
    set test variable       ${ARCHIVE_ID}           ${archive_id}

    ${ARCHIVE_ID_LIST}=     Create List             ${archive_id}
    Set Test Variable       @{ARCHIVE_ID_LIST}
    ${STATUS_LIST}=         Create List
    ...                     ${status}
    Set Test Variable       @{STATUS_LIST}
