*** Settings ***
Variables    aliases.py
Variables    qat_config.py
Resource     actions.robot
Resource     wait_verify.robot
Resource     given.robot
Resource     when.robot
Resource     then.robot
Resource     setup.robot
Resource     teardown.robot
Library      SeleniumLibrary    10
Library      String
Library      lib.qvarn_api_client.QvarnApiClient
...          ${QVARN_URL}    ${CLIENT_ID}    ${CLIENT_SECRET}    ${QVARN_SCOPES}
Library      lib.scim_client.ScimApiClient    ${GLUU_URL}    ${CLIENT_ID}    ${CLIENT_SECRET}    WITH NAME    scim
Library      lib.verify.Verify
Library      lib.utils.Utils

*** Variables ***
${BROWSER}=         headlessfirefox
${BASE_URL}=        http://app:8080
${SP_BASE_URL}=     http://sp
