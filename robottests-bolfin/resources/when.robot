*** Keywords ***
clear filter button is clicked
    [Documentation]    Clear filter button in search or search history page
    Start loading spinner
    Click Element    &{${TESTED_PAGE}_PAGE}[clear-filter]

mouse is hovered on terminate subscription button
    Execute JavaScript    window.scrollTo(${WINDOW_WIDTH}, ${WINDOW_HEIGHT})
    Mouse Over     &{SUBSCRIPTION_PAGE}[terminate-button]

mouse is hovered on hidden report
    # case has been failing because wrong location has been double clicked, will waiting following element to be shown help?
    Wait Until Page Contains    Some of your archived reports are unavailable
    # using Mouse Over was flaky to get tooltip to show, probably because mouse is moved to wrong location
    Double Click Element     &{COMPANY_ELEMENT}[archived-hidden]

mouse is hovered on RP header
    Wait Until Element Is Visible    &{SEARCH_PAGE}[RP-header]
    Mouse Over     &{SEARCH_PAGE}[RP-header]

mouse is hovered on subscription button
    Execute JavaScript    window.scrollTo(${WINDOW_WIDTH}, ${WINDOW_HEIGHT})
    Mouse Over     &{SUBSCRIPTION_PAGE}[subscribe-button]

user accepts terms of use
    Execute JavaScript    window.scrollTo(${WINDOW_WIDTH}, ${WINDOW_HEIGHT})
    Click Element    &{SUBSCRIPTION_PAGE}[tos-checkbox]

user changes search to "${language}" companies
    Select From List By Label    &{SEARCH_PAGE}[search-country]    &{COUNTRY_DROPDOWN}[${language}]

user checks terminate checkbox
    Click Element    &{SUBSCRIPTION_PAGE}[terminate-checkbox]

user checks warning checkbox
    Click Element    &{SUBSCRIPTION_PAGE}[terminate-warning-checkbox]

user clicks terminate button
    Click Element    &{SUBSCRIPTION_PAGE}[terminate-button]

user clicks clear filter button
    [Documentation]    Clear filter button in archive pane
    Click Element    &{COMPANY_ELEMENT}[clear-filter]

user clicks Close view button
    Click Element    &{COMPANY_ELEMENT}[company-view-close]

user clicks "${action}" service description button
    Click Element    &{SUBSCRIPTION_PAGE}[${action}-description]

user clicks report icon for company
    ${windows}=    Get Window Handles
    Set Test Variable    ${WINDOWS}    ${windows}
    Select language  fi
    click report icon in company list    ${COMPANY_ID}

user clicks report icon in archived list
    ${archive_id}=    get archive id    &{USERS}[${USERNAME}]    ${COMPANY_ID}
    Set Test Variable    ${ARCHIVE_ID}    ${archive_id}
    ${windows}=    Get Window Handles
    Set Test Variable    ${WINDOWS}    ${windows}
    click report icon in archived list    ${ARCHIVE_ID}

user clicks subscribe button
    Execute JavaScript    window.scrollTo(${WINDOW_WIDTH}, ${WINDOW_HEIGHT})
    Click Element    &{SUBSCRIPTION_PAGE}[subscribe-button]

user clicks Subscribe now button
    Click Element    &{COMPANY_ELEMENT}[subscribe-now-button]

user clicks View latest report button
    Select Language      fi
    ${windows}=    Get Window Handles
    Set Test Variable    ${WINDOWS}    ${windows}
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Click Element    &{COMPANY_ELEMENT}[view-latest-report-button]

user clicks View latest report button dropdown
    Select Language      fi
    ${windows}=    Get Window Handles
    Set Test Variable    ${WINDOWS}    ${windows}
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Click Element    &{COMPANY_ELEMENT}[view-latest-report-button]
    Click Element    &{COMPANY_ELEMENT}[view-latest-fin-report-button]

user downloads report
    user performs search with "${COMPANY_ID}"
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]   timeout=15
    Open company view    ${COMPANY}
    User clicks View latest report button dropdown
    ${url_contains}=    get expected report url segment    latest    ${COUNTRY_UN}    ${COMPANY_ID}     ${COMPANY}    ''     ${LANGUAGE}
    Verify opened report    title=${EMPTY}    url_contains=${url_contains}

user inserts order reference
    Set Test Variable    ${ORDER_REFERENCE}    12345
    Execute JavaScript    window.scrollTo(${WINDOW_WIDTH}, ${WINDOW_HEIGHT})
    Input Text    &{SUBSCRIPTION_PAGE}[reference-input]    ${ORDER_REFERENCE}

user opens company view in Search history page
    Go to "search history" page
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]       timeout=15
    Open company view    ${COMPANY}
    Wait archived pane is loaded

user opens company view in Search page
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Open company view    ${SEARCH_TEXT}

user opens environment dropdown menu
    Click Element    &{TEMPLATE_ELEMENT}[environment-menu]

user opens user dropdown menu
    Click Element    &{TEMPLATE_ELEMENT}[user-menu]

user orders Report PRO service
    Go to "subscription" page
    Execute JavaScript    window.scrollTo(${WINDOW_WIDTH}, ${WINDOW_HEIGHT})
    Click Element    &{SUBSCRIPTION_PAGE}[tos-checkbox]
    Click Element    &{SUBSCRIPTION_PAGE}[subscribe-button]

user performs search with "${search_string}"
    Input Text    &{SEARCH_PAGE}[search-input]    ${search_string}
    Click Element    &{SEARCH_PAGE}[search-button]

user presses Esc key
    Press Keys    None    ESCAPE

user searches "${search_string}"
    Input Text    &{SEARCH_HISTORY_PAGE}[search-input]    ${search_string}
    Start loading spinner
    Click Element    &{SEARCH_HISTORY_PAGE}[search-button]

user searches "${country}" companies
    Input Text    &{SEARCH_PAGE}[search-input]    ${SEARCH_TEXT}
    Select From List By Label    &{SEARCH_PAGE}[search-country]    &{COUNTRY_DROPDOWN}[${country}]
    Start loading spinner
    Click Element    &{SEARCH_PAGE}[search-button]

user searches company which is ${end_of_kw}
    user searches "${COUNTRY}" companies

user selects company
    ${locator}=    Set Variable    &{COMPANY_ELEMENT}[company]
    Start loading spinner
    Click Element    ${locator.format(string_text="${COMPANY}")}

user selects Reports PRO service
    Click Element    &{SUBSCRIPTION_PAGE}[reports-pro-service]

user selects "${filter}" date
    Execute JavaScript    window.scrollTo(${WINDOW_WIDTH}, ${WINDOW_HEIGHT})
    ${dd}=    Get Time    day
    ${locator}=    Set Variable    &{COMPANY_ELEMENT}[datepicker-date]
    Click Element    &{COMPANY_ELEMENT}[${filter}-date-filter]
    Wait Until Element Is Visible    ${locator.format(day=${dd.lstrip('0')})}
    Click Element    ${locator.format(day=${dd.lstrip('0')})}

user sets filter end date
    ${date}=    get shifted date    shift=-1
    Input Text    &{COMPANY_ELEMENT}[end-date-filter]    ${date}

user sets filter start and end dates
    ${date}=    get shifted date    shift=-1
    Input Text    &{COMPANY_ELEMENT}[start-date-filter]    ${date}
    ${date}=    get shifted date    shift=1
    Input Text    &{COMPANY_ELEMENT}[end-date-filter]    ${date}

user sets filter start date
    ${date}=    get shifted date    shift=1
    Input Text    &{COMPANY_ELEMENT}[start-date-filter]    ${date}
    # A workaround for react-datepicker that would not close until other element receives focus
    Press Keys    &{COMPANY_ELEMENT}[start-date-filter]    TAB

user tries to sign in
    Log in attempt

user unchecks terminate checkbox
    Click Element    &{SUBSCRIPTION_PAGE}[terminate-checkbox]

user unchecks warning checkbox
    Click Element    &{SUBSCRIPTION_PAGE}[terminate-warning-checkbox]

user visits "${page}" page
    Go to "${page}" page

user visits parent company
    [Arguments]     ${successfully}=${TRUE}
    Start loading spinner
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Click Element       &{COMPANY_ELEMENT}[go-to-parent-button]
