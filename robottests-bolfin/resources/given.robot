*** Keywords ***
archive report list was filtered
    archived reports were listed in archived reports pane
    ${date}=    get shifted date    shift=1
    Input Text    &{COMPANY_ELEMENT}[start-date-filter]    ${date}
    ${date}=    get shifted date    shift=2
    Input Text    &{COMPANY_ELEMENT}[end-date-filter]    ${date}
    Table Cell Should Contain    &{COMPANY_ELEMENT}[archived-table]    2    1    &{ELEMENT_TEXT}[no-archived-reports-to-show]

archived reports were listed in archived reports pane
    Report was downloaded during Report PRO subscription
    Go to "search history" page
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Company view on Search history page was open

companies were listed in company list
    Start loading spinner
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    ${company_count}=    Get Element Count    &{COMPANY_ELEMENT}[company-list-row]
    Should Be True    ${company_count} > 1    msg=All expected companies were not listed

company list contains ${_} Partner company
    Search was performed for "${COUNTRY}" companies

company view on Search page was open
    Go to "search" page
    search was performed for "&{UN_TO_LANG}[${COUNTRY_UN}]" companies
    Open company view    ${COMPANY}

company view on Search history page was open
    Go to "search history" page
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Open company view    ${COMPANY}
    Wait archived pane is loaded

filtering was performed for "${filter_string}" ${end_of_kw_name}
    Set Test Variable    ${SEARCH_TEXT}    ${filter_string}
    Input Text    &{SEARCH_HISTORY_PAGE}[search-input]    ${filter_string}
    Start loading spinner
    Click Element    &{SEARCH_HISTORY_PAGE}[search-button]

hidden report was listed in archived reports
    Report was downloaded without Report PRO subscription
    Report PRO was subscribed
    Go to "search history" page
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Company view on Search history page was open
    Execute JavaScript    window.scrollTo(${WINDOW_WIDTH}, ${WINDOW_HEIGHT})

no company is selected
    ${element_count}=    Get Element Count    &{SEARCH_PAGE}[company-selected]
    Should Be Equal As Numbers    ${element_count}    0

organisation contact and invoicing addresses were not configured
    ${contacts}=    get organisation data    &{USERS}[${USERNAME}]    contacts
    Should Be True    '${contacts}' == '[]'

price for Report PRO service was stated to be "${price}" euros
    ${text}=    Set Variable    &{ELEMENT_TEXT}[price]
    Element Should Contain    &{SUBSCRIPTION_PAGE}[price]    ${text.format(price='${price}')}

report was archived for company
    [Arguments]    ${company}=${COMPANY_ID}    ${country}=${COUNTRY_ISO}      ${archive}=${ARCHIVE_ID}
    ${user_data}=    Set Variable    &{USERS}[${USERNAME}]
    Subscribe Report PRO
    create report access    ${archive}    ${country}    ${user_data}[org_id]    ${company}

report was downloaded
    Go to "search" page
    Set Suite Variable    ${SEARCH_TEXT}    ${COMPANY_ID}
    Search for    ${SEARCH_TEXT}    &{UN_TO_LANG}[${COUNTRY_UN}]
    Select language  fi
    ${timestamp}=    open report    ${COMPANY_ID}    ${COMPANY}    ${COUNTRY_UN}    ${LANGUAGE}
    ${access_time}=    wait report access in qvarn    ${timestamp}    &{UN_TO_ISO}[${COUNTRY_UN}]
    wait until timestamp    timestamp=${access_time}    added_seconds=1
    Select language  en

report was downloaded during Report PRO subscription
    Subscribe Report PRO
    Go to "search" page
    Search for    ${COMPANY_ID}    &{UN_TO_LANG}[${COUNTRY_UN}]
    Select language  fi
    open report    ${COMPANY_ID}    ${COMPANY}    ${COUNTRY_UN}     ${LANGUAGE}
    close report windows and company view
    Select language  en

report was downloaded without Report PRO subscription
    set subscription canceled    go_after_page=search
    Search for    ${COMPANY_ID}    &{UN_TO_LANG}[${COUNTRY_UN}]
    Select language  fi
    open report    ${COMPANY_ID}    ${COMPANY}    ${COUNTRY_UN}     ${LANGUAGE}
    close report windows and company view
    Select language  en

Report PRO was active
    Go to "search" page
    Wait Until Element Contains    &{SEARCH_PAGE}[info-message]    &{ELEMENT_TEXT}[report-pro-active]   timeout=15

Report PRO was not subscribed
    set subscription canceled

Report PRO was subscribed
    Subscribe Report PRO

search error message was shown
    Input Text    &{SEARCH_PAGE}[search-input]    a
    Click Element    &{SEARCH_PAGE}[search-button]
    Wait Until Element Is Visible    &{SEARCH_PAGE}[error-message]

search text box was empty
    # BOL doesn't think search string to be empty if Clear Element Text keyword is used
    Input Text    &{SEARCH_PAGE}[search-input]    t
    Press Keys    &{SEARCH_PAGE}[search-input]    BACKSPACE
    Element Text Should Be    &{SEARCH_PAGE}[search-input]    ${EMPTY}

search text was "${search_string}"
    Input Text    &{SEARCH_PAGE}[search-input]    ${search_string}

search was performed for "${country}" companies
    Input Text    &{SEARCH_PAGE}[search-input]    ${SEARCH_TEXT}
    Select From List By Label    &{SEARCH_PAGE}[search-country]    &{COUNTRY_DROPDOWN}[${country}]
    Start loading spinner
    Click Element    &{SEARCH_PAGE}[search-button]
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]

service description was hidden
    Element Should Not Be Visible    &{SUBSCRIPTION_PAGE}[description]

service description was shown
    Element Should Be Visible    &{SUBSCRIPTION_PAGE}[description]

subscribe button was disabled
    Verify button is disabled    &{SUBSCRIPTION_PAGE}[subscribe-button]

subscription was terminated
    Terminate subscription

terminate button was enabled
    Verify button is enabled    &{SUBSCRIPTION_PAGE}[terminate-button]

terminate checkbox was checked
    set checkbox checked    terminate-checkbox
    Wait Until Element is Visible    &{TEMPLATE_ELEMENT}[danger-alert]
    Wait Until Element is Visible    &{SUBSCRIPTION_PAGE}[terminate-warning-checkbox]

terminate checkbox was unchecked
    Checkbox Should Not Be Selected    &{SUBSCRIPTION_PAGE}[terminate-checkbox-status]

terminate subscription button was disabled
    Verify button is disabled    &{SUBSCRIPTION_PAGE}[terminate-button]

text was set in start and end date inputs
    archived reports were listed in archived reports pane
    Input Text    &{COMPANY_ELEMENT}[start-date-filter]    some text
    Input Text    &{COMPANY_ELEMENT}[end-date-filter]    some text

too many results warning was shown
    Search for    Oy    Finnish
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]     timeout=15
    Verify CSS property value    &{TEMPLATE_ELEMENT}[warning-alert]    background-color    'value' == '&{CSS}[alert-warning-background-color]'

user was on sign in page
    Wait until page contains element    &{SIGN_IN_PAGE}[sign-in-button]

user was on "${page}" page
    Select Language     en
    Wait until page contains element    &{${page.upper()}_PAGE}[page-id]    timeout=15

warning checkbox was checked
    Click Element    &{SUBSCRIPTION_PAGE}[terminate-warning-checkbox]

"${user}" was logged in
    Element Should Contain    &{TEMPLATE_ELEMENT}[user-menu]    ${user}
    Select Language     en

multiple reports were archived with archive_ids: "@{archive_id_list}"
    ${user_data}=           Set Variable            &{USERS}[${USERNAME}]

    FOR     ${id}    IN   @{archive_id_list}
        create report access    archive_id=${id}      country=${COUNTRY_ISO}      active_org_id=${user_data}[org_id]  identifier=${COMPANY_ID}
    END

sp language was selected as "${lang}"
    Go To                                           ${SP_BASE_URL}
    Wait until page contains                        Welcome to your company account     timeout=30s
    SP wait for page to load
    Click element                                   &{SP_ELEMENTS}[user-menu]
    Wait until element is visible                   &{SP_ELEMENTS}[language]${lang}
    Click element                                   &{SP_ELEMENTS}[language]${lang}
    SP wait for page to load
    Go to                                           ${BASE_URL}
    Wait Until Page Does Not Contain Element        &{TEMPLATE_ELEMENT}[loading-spinner]
    Ensure Language                                 ${lang}
