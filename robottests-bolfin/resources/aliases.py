LANDING_PAGE = {'sign-in-button': 'css=#login_page_signin_link'}

SIGN_IN_PAGE = {
    'username-input': 'id=loginForm:username',
    'password-input': 'id=loginForm:password',
    'sign-in-button': 'id=loginForm:loginButton',
}

ON_PAGE = {'search': '/#/search', 'search history': '/#/companies', 'subscription': '/#/subscription'}

TEMPLATE_ELEMENT = {
    'footer': 'footer',
    'loading-spinner': 'css=.fa-spinner',
    'user-menu': 'top_menu_user_dropdown',
    'environment-menu': 'top_menu_enviroment_dropdown',
    'stv-footer': 'css=.stv-footer',
    'success-alert': 'xpath=//*[@class="alert alert-success"]',
    'warning-alert': 'xpath=//*[@class="alert alert-warning"]',
    'danger-alert': 'xpath=//*[contains(@class, "alert-danger")]',
    'language-en': 'top_menu_user_info_lang_en',
    'language-sv': 'top_menu_user_info_lang_sv',
    'language-fi': 'top_menu_user_info_lang_fi',
}

SEARCH_PAGE = {
    'page-id': 'css=#search_page',
    'search-input': 'name=compsearch',
    'search-country': 'name=company_country',
    'search-button': 'css=#search_filter_button',
    'clear-filter': 'css=#clear-filter-link',
    'company-selected': 'css=.table-active',
    'error-message': 'css=.text-danger',
    'warning-message': 'css=#search_warning_message',
    'info-message': 'css=#search_info_message',
    'RP-header': 'xpath=//span[.="RP"]',
}

COUNTRY_DROPDOWN = {
    'Finnish': 'Finland',
    'Estonian': 'Estonia',
    'Swedish': 'Sweden',
    'Lithuanian': 'Lithuania',
    'Polish': 'Poland',
    'Latvian': 'Latvia',
}

LANG_TO_UN = {'Finnish': 'FIN', 'Estonian': 'EST', 'Swedish': 'SWE', 'Lithuanian': 'LTU', 'Polish': 'POL', 'Latvian': 'LVA'}

LANG_TO_ISO = {'Finnish': 'FI', 'Estonian': 'EE', 'Swedish': 'SV', 'Lithuanian': 'LT', 'Polish': 'PL', 'Latvian': 'LV', 'English': 'EN'}

UN_TO_LANG = {abbr: lang for lang, abbr in LANG_TO_UN.items()}

UN_TO_ISO = {'FIN': 'FI', 'EST': 'EE', 'SWE': 'SE', 'LTU': 'LT', 'POL': 'PL', 'LVA': 'LV'}

ELEMENT_TEXT = {
    'subscription-success': 'You have successfully subscribed to the service!',
    'subscribe-page': 'The annual fee is',
    'price': 'The annual fee is {price} € + VAT',
    'company-view-subscribe-button': 'Subscribe now!',
    'tos-checkbox': 'I accept',
    'subscribe-button': 'Subscribe to Report PRO service',
    'terminate-checkbox': 'I wish to terminate',
    'terminate-warning-checkbox': 'I have read',
    'terminate-button': 'Terminate the Report PRO service subscription',
    'hide-description': 'Hide service description',
    'show-description': 'Show service description',
    'cancel-success': 'You have successfully terminated your subscription to the Report PRO service.',
    'basic-user-subscribe-page-active': 'Report PRO service is active. You do not have permission to manage the subscription.',
    'basic-user-subscribe-page-not-active': 'You do not have permission to order the service.',
    'basic-user-archive-message': 'Ask the main user of your company about subscribing the Report PRO service',
    'archive-service-terminated': 'Your archived reports are unavailable, because your Report PRO license has been terminated.',
    'archive-downloaded-before': 'Some of your archived reports are unavailable, because they were fetched before your current subscription began.',
    'not-to-be-archived': 'This report will not be archived. Only Finnish and Estonian company reports are archived.',
    'no-archived-reports-to-show': 'There is no data to display',
    'vague-search': 'Too many results',
    'report-pro-active': 'The Report PRO service is in use',
}

COMPANY_ELEMENT = {
    'company-list-row': 'xpath=//*[contains(@class, "company-list")]/tbody/tr',
    'company-table': 'css=.company-list',
    'company': 'xpath=//*[contains(text(),"{string_text}")]',
    'company-list-reliable-partner': 'css=.company-list > tbody > tr:first-child > td:nth-child(3)',
    'company-list-rp-check-mark': 'css=.company-list > tbody > tr:first-child > td:nth-child(3) > i',
    'company-view': 'css=.company-details-view',
    'company-view-title': 'css=.modal-title',
    'company-name-info': 'css=.company-name',
    'company-id-info': 'xpath=(//span[@class="business-id"])',
    'company-country-info': 'css=.country',
    'company-status-ok-icon': 'xpath=//i[contains(@class, "icon-status-ok-stv")]',
    'company-status-ok-text': 'xpath=//span[contains(@class, "status-cell") and contains(@class, "status-ok")]',
    'company-status-attention-icon': 'xpath=//i[contains(@class, "status-cell") and contains(@class, "icon-status-attention-stv")]',
    'company-status-attention-text': 'xpath=//span[contains(@class, "status-cell") and contains(@class, "status-attention")]',
    'company-status-investigate-icon': 'xpath=//i[contains(@class, "status-cell") and contains(@class, "icon-status-investigate-stv")]',
    'company-status-investigate-text': 'xpath=//span[contains(@class, "status-cell") and contains(@class, "status-investigate")]',
    'company-status-incomplete-icon': 'xpath=//i[contains(@class, "status-cell") and contains(@class, "icon-status-incomplete-stv")]',
    'company-status-incomplete-text': 'xpath=//span[contains(@class, "status-cell") and contains(@class, "status-incomplete")]',
    'company-status-warning-icon': 'xpath=//i[contains(@class, "status-cell") and contains(@class, "icon-status-stop-stv")]',
    'company-status-warning-text': 'xpath=//span[contains(@class, "status-cell") and contains(@class, "status-stop")]',
    'company-view-close': 'css=#close_view_button_title',
    'view-latest-report-button': 'css=#company_report_link',
    'view-latest-fin-report-button': 'css=.dropdown-in-finnish.dropdown-item',
    'alert': 'css=#company-alert-warning',
    'subscribe-now-button': 'xpath=//span[contains(text(),"{}")]'.format(
        ELEMENT_TEXT['company-view-subscribe-button']
    ),
    'report-icon-link': 'xpath://table//tbody//tr[{row}]//td[{column}]//a',
    'archive-report-icon': 'xpath://table//tbody//tr[{row}]//td[{column}]//span',
    'update-date': 'xpath=(//div[@class="details-list--list"])[2]',
    'archived-reports-pane': 'css=.tab-pane',
    'archived-table': 'css=.archived-reports > table',
    'archived-table-row': 'css=.archived-reports > table > tbody > tr',
    'archived-table-status': 'css=.archived-reports > table > tbody > tr:nth-child({row}) > td > i',
    'archived-hidden': 'xpath=//td/span[contains(text(),"Unavailable")][1]',
    'start-date-filter': 'css=#start_date',
    'end-date-filter': 'css=#end_date',
    'clear-filter': 'xpath=//div[@class="tab-pane active"]//*[@id="clear-filter-link"]',
    'zeckit': 'xpath=//iframe[@title="Zeckit widget"]',
    'zeckit-header-link': 'xpath=//a[@href="https://zeckit.com/"]',
    'date-picker': 'css=.rdtPicker',
    'datepicker-date': 'xpath=//div[contains(@class, "rdtOpen")]//td[contains(@class, "rdtDay") and .="{day}"]',
    'datepicker-next-month': 'xpath=//div[contains(@class, "rdtOpen")]//th[@class="rdtNext"]',
    'datepicker-prev-month': 'xpath=//div[contains(@class, "rdtOpen")]//th[@class="rdtPrev"]',
    'go-to-parent-button': 'id=goto_parent_company_link',
    'rala-competence-logo': 'xpath=//img[@alt="RALA competence"]',
    'rala-certificate-logo': 'xpath=//img[@alt="RALA certificate"]',
}

SEARCH_HISTORY_PAGE = {
    'page-id': 'css=#companies_page',
    'search-input': 'name=compsearch',
    'search-button': 'css=#companies_filter_button',
    'clear-filter': 'css=#clear-filter-link',
    'company-selected': 'css=.table-active',
    'company-selected-status': 'xpath=//span[@class="company-status"]'
}

SUBSCRIPTION_PAGE = {
    'page-id': 'css=#subscriptions_page',
    'tos-checkbox': 'xpath=//span[contains(text(),"{}")]'.format(ELEMENT_TEXT['tos-checkbox']),
    'tos-checkbox-status': 'xpath=//span[contains(text(),"{}")]/../../../input'.format(
        ELEMENT_TEXT['tos-checkbox']
    ),
    'subscribe-button': 'xpath=//span[contains(text(),"{}")]'.format(
        ELEMENT_TEXT['subscribe-button']
    ),
    'reference-input': 'css=.form-control',
    'terminate-checkbox': 'xpath=//span[contains(text(),"{}")]'.format(
        ELEMENT_TEXT['terminate-checkbox']
    ),
    'terminate-checkbox-status': 'xpath=//span[contains(text(),"{}")]/../../../input'.format(
        ELEMENT_TEXT['terminate-checkbox']
    ),
    'terminate-warning-checkbox': 'xpath=//span[contains(text(),"{}")]'.format(
        ELEMENT_TEXT['terminate-warning-checkbox']
    ),
    'terminate-warning-checkbox-status': 'xpath=//span[contains(text(),"{}")]/../../../input'.format(
        ELEMENT_TEXT['terminate-warning-checkbox']
    ),
    'terminate-button': 'xpath=//span[contains(text(),"{}")]'.format(
        ELEMENT_TEXT['terminate-button']
    ),
    'service-description': 'css=.SubscriptionMarketingComponent-header',
    'price': 'css=p.lead',
    'description': 'css=.SubscriptionMarketingComponent-header',
    'hide-description': 'xpath=//span[.="{}"]'.format(ELEMENT_TEXT['hide-description']),
    'show-description': 'xpath=//span[.="{}"]'.format(ELEMENT_TEXT['show-description']),
}

USER_DROPDOWN = {
    'lang-suomi': 'css=#top_menu_user_info_lang_fi',
    'sign-out': 'css=#top_menu_user_info_signout',
}

ENVIRONMENT_DROPDDDOWN = {
    'services': 'xpath=//*[@id="top_menu_enviroment_dropdown"]/following-sibling::div/a'
}

ENVIRONMENT_LINKS = {
    'home': 'css=#top_menu_enviroment_home',
    'report': 'css=#top_menu_enviroment_tilaajavastuu_report',
    'valvoja': 'css=#top_menu_enviroment_tilaajavastuu_supervisor',
    'ilmoita': 'css=#top_menu_enviroment_taxnumber_report',
    'taito': 'css=#top_menu_enviroment_competency_home',
    'site-register': 'css=#top_menu_enviroment_taxnumber_register',
}

CSS = {
    'ok-color': 'rgb(105, 190, 40)',
    'attention-color': 'rgb(50, 161, 209)',
    'investigate-color': 'rgb(244, 159, 12)',
    'incomplete-color': 'rgb(153, 153, 153)',
    'warning-color': 'rgb(216, 0, 39)',
    'alert-success-background-color': 'rgb(232, 253, 244)',
    'alert-warning-background-color': 'rgb(255, 250, 220)',
    'hidden-color': 'rgb(153, 153, 153)',
    'available-color': 'rgb(54, 54, 54)',
    'danger-color': 'rgb(216, 0, 39)',
    'rp-check-mark': 'fa fa-check',
}

IMAGE = {
    'reliable-partner': '/static/img/reliable_partner_en.png',
    'ok-icon': '/static/img/icon-ok-stv.png',
    'attention-icon': '/static/img/icon-attention-stv.png',
    'investigate-icon': '/static/img/icon-investigate-stv.png',
    'incomplete-icon': '/static/img/icon-incomplete-stv.png',
    'warning-icon': '/static/img/icon-stop-stv.png',
}

REPORT_URL = {
    'latest': 'company/{country}/{org_id}/{language}/report/{company}_{org_id}_Luotettava_Kumppani_raportti_{date}.pdf',
    'archived': 'company/{country}/{org_id}/archived-reports/{archive_id}/{company}_{org_id}_Luotettava_Kumppani_raportti_{date}.pdf',
}

TOOLTIP = {
    'subscribe-button': {
        'locator': '{}/../..'.format(SUBSCRIPTION_PAGE['subscribe-button']),
        'attribute': 'data-tip',
    },
    'terminate-button': {
        'locator': '{}/../..'.format(SUBSCRIPTION_PAGE['terminate-button']),
        'attribute': 'data-tip',
    },
    'hidden-report': {
        'locator': '{}/..'.format(COMPANY_ELEMENT['archived-hidden']),
        'attribute': 'data-tip',
    },
    'RP-header': {'locator': '{}/..'.format(SEARCH_PAGE['RP-header']), 'attribute': 'title'},
}

SP_ELEMENTS = {
    'loading-indicator': 'id=PageIsLoading',
    'footer': 'id=footer',
    'user-menu': 'id=top_menu_user_dropdown',
    'language': 'id=top_menu_user_info_lang_'
}
