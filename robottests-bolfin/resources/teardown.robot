*** Keywords ***
Archive test teardown
    close report windows and company view
    set subscription canceled    go_after_page=search history

Archive suite teardown
    [Arguments]    ${countries}
    delete report accesses    ${countries}
    delete subscriptions    &{USERS}[${USERNAME}]
    Close All Browsers
    Browser setup
    Log in

Basic user suite teardown with active subscription
    [Arguments]    ${main_user}    ${countries}
    Close All Browsers
    delete report accesses    ${countries}
    delete subscriptions    &{USERS}[${main_user}]

Terminate subscription and delete subscription data from Qvarn
    set subscription canceled
    delete subscriptions    &{USERS}[${USERNAME}]

Terminate subscription and delete subscription data from Qvarn for companies of "${user}"
    set subscription canceled
    delete subscriptions    &{USERS}[${user}]

Terminate subscription and go to "${page}" page
    Terminate subscription
    Go to "${page}" page

Vague search case teardown
    [Documentation]    Wait loading spinner to be removed and clear search filtering
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]    timeout=15
    Start loading spinner
    Click Element    &{${TESTED_PAGE}_PAGE}[clear-filter]
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
