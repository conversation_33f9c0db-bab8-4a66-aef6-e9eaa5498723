GLUU_URL = 'https://auth-azure-alpha.id06.se'
QVARN_URL = 'http://haproxy'
CLIENT_ID = '@!F245.8207.02D2.AA4E!0001!EF88.84B4!0008!2BA0.454F'
CLIENT_SECRET = 'supersecret'
QVARN_DATA = 'resources/qvarn.json'
QVARN_SCOPES = [
    'uapi_contracts_get',
    'uapi_contracts_post',
    'uapi_contracts_id_delete',
    'uapi_contracts_id_document_get',
    'uapi_contracts_id_document_put',
    'uapi_contracts_id_get',
    'uapi_contracts_id_put',
    'uapi_contracts_search_id_get',
    'uapi_orgs_get',
    'uapi_orgs_post',
    'uapi_orgs_id_get',
    'uapi_orgs_id_put',
    'uapi_orgs_id_delete',
    'uapi_orgs_id_sync_get',
    'uapi_orgs_search_id_get',
    'uapi_persons_get',
    'uapi_persons_id_delete',
    'uapi_persons_id_get',
    'uapi_persons_id_private_get',
    'uapi_persons_id_private_put',
    'uapi_persons_id_put',
    'uapi_persons_post',
    'uapi_persons_search_id_get',
    'uapi_reports_get',
    'uapi_reports_post',
    'uapi_reports_id_get',
    'uapi_reports_id_delete',
    'uapi_reports_id_pdf_get',
    'uapi_reports_id_pdf_put',
    'uapi_reports_search_id_get',
    'uapi_report_accesses_search_id_get',
    'uapi_report_accesses_id_get',
    'uapi_report_accesses_post',
    'uapi_report_accesses_id_delete',
    'uapi_services_search_id_get',
    'uapi_services_get',
    'uapi_services_post',
    'uapi_services_id_get',
    'uapi_service_plans_post',
    'uapi_service_plans_search_id_get',
    'uapi_service_plans_id_get',
]
