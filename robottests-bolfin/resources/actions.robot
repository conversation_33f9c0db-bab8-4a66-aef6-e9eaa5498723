*** Keywords ***
Go to "${page}" page
    Set Global Variable    ${TESTED_PAGE}    ${page.upper().replace(' ', '_')}
    Go To    ${BASE_URL}&{ON_PAGE}[${page}]
    Wait Until Page Contains Element    &{${page.upper()}_PAGE}[page-id]    timeout=15
    Wait Until Element Is Visible    &{TEMPLATE_ELEMENT}[footer]            timeout=15
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]   timeout=15

Close company view
    Execute JavaScript    window.scrollTo(0, 0)
    Wait Until Element Is Visible    &{COMPANY_ELEMENT}[company-view-close]
    Press Keys    None    ESCAPE
    Wait Until Element Is Not Visible    &{COMPANY_ELEMENT}[company-view]

Log in
    Log in attempt
    Wait Until Element Is Visible    &{TEMPLATE_ELEMENT}[user-menu]     timeout=15
    Wait Until Element Is Visible    &{TEMPLATE_ELEMENT}[footer]        timeout=15
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Select Language     en

Log in attempt
    ${user_data}=                       Set Variable    &{USERS}[${USERNAME}]
    ${password}=                        Set Variable    &{user_data}[password]
    update user gluu data               ${user_data}
    Wait Until Element Is Visible       &{SIGN_IN_PAGE}[sign-in-button]
    Input Text                          &{SIGN_IN_PAGE}[username-input]    ${USERNAME}
    Input Password                      &{SIGN_IN_PAGE}[password-input]    &{user_data}[password]
    Click Element                       &{SIGN_IN_PAGE}[sign-in-button]

Open company view
    [Arguments]    ${search_text}
    ${locator}=    Set Variable    &{COMPANY_ELEMENT}[company]
    Start loading spinner
    Click Element    ${locator.format(string_text="${search_text}")}
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Wait Until Element Is Visible    &{COMPANY_ELEMENT}[company-view]

Search for
    [Arguments]    ${search_string}    ${country}
    Input Text    &{SEARCH_PAGE}[search-input]    ${search_string}
    Select From List By Label    &{SEARCH_PAGE}[search-country]    &{COUNTRY_DROPDOWN}[${country}]
    Start loading spinner
    Click Element    &{SEARCH_PAGE}[search-button]
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]    timeout=15
    Element Should Contain    &{COMPANY_ELEMENT}[company-list-row]    ${search_string}    ignore_case=True

Start loading spinner
    Execute Javascript    document.getElementsByClassName("company-list")[0].className += " loading"

Subscribe Report PRO
    ${user_data}=    Set Variable    &{USERS}[${USERNAME}]
    Select language  en
    Go to "subscription" page
    Execute JavaScript    window.scrollTo(${WINDOW_WIDTH}, ${WINDOW_HEIGHT})
    Click Element    &{SUBSCRIPTION_PAGE}[tos-checkbox]
    Click Element    &{SUBSCRIPTION_PAGE}[subscribe-button]
    Wait Until Element is Visible    &{SUBSCRIPTION_PAGE}[terminate-button]
    wait subscription in Qvarn    &{user_data}[customership_id]

Terminate subscription
    Select language  en
    Go to "subscription" page
    Wait Until Element Is Visible    &{SUBSCRIPTION_PAGE}[terminate-button]
    set checkbox checked   terminate-checkbox
    Wait Until Element Is Visible    &{SUBSCRIPTION_PAGE}[terminate-warning-checkbox]
    set checkbox checked   terminate-warning-checkbox
    Click Element    &{SUBSCRIPTION_PAGE}[terminate-button]
    Wait Until Page Contains    &{ELEMENT_TEXT}[subscribe-page]

Verify Element
    [Arguments]     ${element}
    Wait Until Keyword Succeeds    ${SLEEP_TIME_WHEN_VERIFYING}    ${RETRY_INTERVAL}    Page Should Contain Element    ${element}   loglevel=NONE

SP page is loaded
    ${footer_exists}=  Run Keyword And Return Status       Verify Element     &{SP_ELEMENTS}[footer]
    Run Keyword If    ${footer_exists} == True      Wait Until Keyword Succeeds    20s    1s    Element Should Be Visible    &{SP_ELEMENTS}[footer]
    Wait Until Keyword Succeeds    20s    1s    Run Keywords    Element Should Not Be Visible    &{SP_ELEMENTS}[loading-indicator]    AND    Wait Until Page Does Not Contain Element    &{SP_ELEMENTS}[loading-indicator]

SP wait for page to load
    Wait Until Keyword Succeeds    20s    1s    SP page is loaded
    # somethimes the loading indicator appears twice, once for loading the page, once for loading content
    Wait Until Keyword Succeeds    20s    1s    SP page is loaded
