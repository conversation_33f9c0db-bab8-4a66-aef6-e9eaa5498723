*** Keywords ***
all Vastuu Group services will be listed
    verify environment dropdown menu services

archive ID and download date will be shown
    ${archive_id}=    get archive id    &{USERS}[${USERNAME}]    ${COMPANY_ID}
    Table Cell Should Contain    &{COMPANY_ELEMENT}[archived-table]    2    2    ${archive_id}
    ${yyyy}    ${mm}    ${dd}=    Get Time    year,month,day
    Table Cell Should Contain    &{COMPANY_ELEMENT}[archived-table]    2    3    ${mm}/${dd}/${yyyy}

archived report list will be unfiltered
    In archive pane report status and report will be available

archived report will be opened in new window
    ${url_contains}=    get expected report url segment    archived    &{UN_TO_ISO}[${COUNTRY_UN}]    ${COMPANY_ID}     ${COMPANY}    ${ARCHIVE_ID}     ${LANGUAGE}
    wait new window is opened    ${WINDOWS}
    Verify opened report    title=${EMPTY}    url_contains=${url_contains}

check reliable partner info
    [Arguments]     ${report_status}    ${company_name}     ${company_id}   ${country_un}
    ${status}=    Set Variable    ${report_status.lower().replace('!', '')}
    Wait company view is loaded
    Wait until element is visible         &{COMPANY_ELEMENT}[company-name-info]
    Element Text Should Be    &{COMPANY_ELEMENT}[company-view-title]        ${company_name}
    Element Text Should Be    &{COMPANY_ELEMENT}[company-name-info]         ${company_name}
    Element Text Should Be    &{COMPANY_ELEMENT}[company-id-info]           ${company_id}
    Element Text Should Be    &{COMPANY_ELEMENT}[company-country-info]      ${country_un}
    Element Text Should Be    &{COMPANY_ELEMENT}[company-status-${status}-text]    ${report_status}
    Verify CSS property value    &{COMPANY_ELEMENT}[company-status-${status}-text]    color    'value' == '&{CSS}[${status}-color]'
    Element Should Be Visible    &{COMPANY_ELEMENT}[company-status-${status}-icon]
    Verify CSS property value    &{COMPANY_ELEMENT}[company-status-${status}-icon]    background-image    'value' == 'url("${BASE_URL}&{IMAGE}[${status}-icon]")'

parent company and reliable partner information will be shown
    check reliable partner info     report_status=${PARENT_REPORT_STATUS}       company_name=${PARENT_COMPANY}
    ...                             company_id=${PARENT_COMPANY_ID}             country_un=${PARENT_COUNTRY_UN}

company and reliable partner information will be shown
    check reliable partner info     report_status=${REPORT_STATUS}       company_name=${COMPANY}
    ...                             company_id=${COMPANY_ID}             country_un=${COUNTRY_UN}

company list will be cleared
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    verify company list is empty
    verify company search field is empty

company list will be filtered with "${filter_string}" ${end_of_kw_name}
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    verify company filtering    ${filter_string}

company list will be loaded in "${seconds}" seconds
    ${start_time}=    Get Time    epoch
    Start loading spinner
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    ${stop_time}=    Get Time    epoch
    Should Be True    ${${stop_time} - ${start_time}} < ${seconds}    msg=Loading took too long

company list will show all companies
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    verify company list is filled    &{USERS}[${USERNAME}]

company row will be highlighted
    Wait Until Element Contains    &{SEARCH_PAGE}[company-selected]    ${COMPANY}
    ${element_count}=    Get Element Count    &{SEARCH_PAGE}[company-selected]
    Should Be Equal As Numbers    ${element_count}    1

company view will be closed
   Wait Until Element Is Not Visible    &{COMPANY_ELEMENT}[company-view]

company view will not show report cannot be archived message
    Wait Until Element Is Visible    &{COMPANY_ELEMENT}[company-view]
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Page Should Not Contain Element    &{COMPANY_ELEMENT}[alert]

company view will show message to contact main user about Report PRO
    Wait Until Element Is Visible    &{COMPANY_ELEMENT}[company-view]
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Wait archived pane is loaded
    Page Should Contain    &{ELEMENT_TEXT}[basic-user-archive-message]

company view will show report cannot be archived message
    Wait Until Element Is Visible    &{COMPANY_ELEMENT}[company-view]
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Element Text Should Be    &{COMPANY_ELEMENT}[alert]    &{ELEMENT_TEXT}[not-to-be-archived]

company will not be listed in Search history
    Go to "search history" page
    Start loading spinner
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Page Should Not Contain    ${COMPANY_ID}

company view will show "${fi_id}" for Finnish business ID
    Wait company view is loaded
    ${id_count}=    Get Element Count    &{COMPANY_ELEMENT}[company-id-info]
    Should Be Equal As Numbers    ${id_count}    2
    Element Text Should Be    &{COMPANY_ELEMENT}[company-id-info][2]    ${fi_id}

company view will show "${date}" for report update date
    Wait company view is loaded
    Element Should Contain    &{COMPANY_ELEMENT}[update-date]    Report updated: ${date}

company will be listed as unknown
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    verify unknown listed company    ${COMPANY_ID}

date picker will be closed
    Page Should Contain Element    &{COMPANY_ELEMENT}[date-picker]
    Element Should Not Be Visible    &{COMPANY_ELEMENT}[date-picker]

description will inform about reports downloaded before current subscription
    Element Should Contain   &{COMPANY_ELEMENT}[archived-reports-pane]    ${ELEMENT_TEXT}[archive-downloaded-before]

description will inform about terminated licence
    Element Should Contain   &{COMPANY_ELEMENT}[archived-reports-pane]    ${ELEMENT_TEXT}[archive-service-terminated]

error message containing "${message}" will be shown
    verify message contains    danger    ${message}    timeout=15
    Verify CSS property value    &{TEMPLATE_ELEMENT}[danger-alert]    color    'value' == '&{CSS}[danger-color]'

in archive pane report status and report will not be available
    ${row_count}=    Get Element Count    &{COMPANY_ELEMENT}[archived-table-row]
    Should Be Equal As Numbers    ${row_count}    1
    Table Cell Should Contain    &{COMPANY_ELEMENT}[archived-table]    2    1    Unavailable
    Verify CSS property value    &{COMPANY_ELEMENT}[archived-table-row]:nth-child(1)    cursor    'value' == 'not-allowed'
    Verify CSS property value    &{COMPANY_ELEMENT}[archived-table-row]:nth-child(1)    color    'value' == '&{CSS}[hidden-color]'

in archive pane report status and report will be available
    ${status}=    Set Variable    ${REPORT_STATUS.lower().replace('!', '')}
    ${status_icon}=    Set Variable    &{COMPANY_ELEMENT}[archived-table-status]
    ${row_count}=    Get Element Count    &{COMPANY_ELEMENT}[archived-table-row]
    Should Be Equal As Numbers    ${row_count}    1
    Verify CSS property value    &{COMPANY_ELEMENT}[archived-table-row]:nth-child(1)    cursor    'value' == 'pointer'
    Verify CSS property value    ${statusicon.format(row=1)}    background-image    'value' == 'url("${BASE_URL}&{IMAGE}[${status}-icon]")'
    Verify CSS property value    &{COMPANY_ELEMENT}[archived-table-row]:nth-child(1)    color    'value' == '&{CSS}[available-color]'

message about missing permissions will be shown
    Element Should Contain    &{TEMPLATE_ELEMENT}[warning-alert]    &{ELEMENT_TEXT}[basic-user-subscribe-page-${SUBSCRIPTION_STATE}]

order reference will be stored to Qvarn
    ${reference}=    get order reference    &{USERS}[${USERNAME}]
    Should Be Equal    ${ORDER_REFERENCE}    ${reference}

only reports downloaded after re-subscription will be available
    ${status}=    Set Variable    ${REPORT_STATUS.lower().replace('!', '')}
    ${status_icon}=    Set Variable    &{COMPANY_ELEMENT}[archived-table-status]
    ${row_count}=    Get Element Count    &{COMPANY_ELEMENT}[archived-table-row]
    Should Be Equal As Numbers    ${row_count}    2
    Verify CSS property value    &{COMPANY_ELEMENT}[archived-table-row]:nth-child(1)    cursor    'value' == 'pointer'
    Verify CSS property value    ${statusicon.format(row=1)}    background-image    'value' == 'url("${BASE_URL}&{IMAGE}[${status}-icon]")'
    Verify CSS property value    &{COMPANY_ELEMENT}[archived-table-row]:nth-child(1)    color    'value' == '&{CSS}[available-color]'
    Table Cell Should Contain    &{COMPANY_ELEMENT}[archived-table]    3    1    Unavailable
    Verify CSS property value    &{COMPANY_ELEMENT}[archived-table-row]:nth-child(2)    cursor    'value' == 'not-allowed'
    Verify CSS property value    &{COMPANY_ELEMENT}[archived-table-row]:nth-child(2)    color    'value' == '&{CSS}[hidden-color]'

page will not contain "${button}" button
    Page Should Not Contain    &{ELEMENT_TEXT}[${button}-button]

price "${price}" will be stored to Qvarn
    Wait Until Element is Visible    &{SUBSCRIPTION_PAGE}[terminate-button]
    ${qvarn_price}=    get stored price    &{USERS}[${USERNAME}]
    Should Be Equal As Numbers   ${price}    ${qvarn_price}

Reliable Partner check mark will be in company list
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    verify reliable partner check mark is shown

Reliable Partner check mark will not be in company list
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Wait Until Element Contains    &{COMPANY_ELEMENT}[company-list-reliable-partner]    -

Reliable Partner logo will be shown for the company
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    verify reliable partner logo is shown    ${BASE_URL}

Reliable Partner logo will not be shown for the company
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
    Wait Until Element Contains    &{COMPANY_ELEMENT}[company-list-reliable-partner]    -

report will be archived
    Go to "search history" page
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]   timeout=15
    Open company view    ${COMPANY}
    Wait archived pane is loaded
    ${row_count}=    Get Element Count    &{COMPANY_ELEMENT}[archived-table-row]
    Should Be Equal As Numbers    ${row_count}    1
    Verify CSS property value    &{COMPANY_ELEMENT}[archived-table-row]:nth-child(1)    cursor    'value' == 'pointer'
    Verify CSS property value    &{COMPANY_ELEMENT}[archived-table-row]:nth-child(1)    color    'value' == '&{CSS}[available-color]'

report will be opened in new window
    wait new window is opened    ${WINDOWS}
    Verify opened report    title=${EMPTY}    url_contains=company/${COUNTRY_UN}/${COMPANY_ID}/${LANGUAGE}/report/

reports between filter dates will be shown
    In archive pane report status and report will be available

Report PRO upgrade message will be shown
    Element Should Contain    &{SEARCH_PAGE}[info-message]    Upgrade the service

reports will be filtered out ${end_of_kw}
    Table Cell Should Contain    &{COMPANY_ELEMENT}[archived-table]    2    1    &{ELEMENT_TEXT}[no-archived-reports-to-show]

search error message will be removed
    Wait Until Element Is Not Visible    &{SEARCH_PAGE}[error-message]

search error "${message}" will be shown
    Wait Until Element Is Visible    &{SEARCH_PAGE}[error-message]
    Element Should Contain    &{SEARCH_PAGE}[error-message]    ${message}

search text will be cleared
    Wait Until Element Does Not Contain    &{SEARCH_PAGE}[search-input]    ${SEARCH_TEXT}

service description will be hidden
    Wait Until Element Is Not Visible    &{SUBSCRIPTION_PAGE}[description]

service description will be shown
    Wait Until Element Is Visible    &{SUBSCRIPTION_PAGE}[description]

start and end date inputs will be cleared
    Textfield Value Should Be    &{COMPANY_ELEMENT}[start-date-filter]    ${EMPTY}
    Textfield Value Should Be    &{COMPANY_ELEMENT}[end-date-filter]    ${EMPTY}

subscribe button will be enabled
    Verify button is enabled    &{SUBSCRIPTION_PAGE}[subscribe-button]

subscribe page will be loaded
    Wait Until Element Is Visible    &{SUBSCRIPTION_PAGE}[subscribe-button]
    verify message contains    success    &{ELEMENT_TEXT}[cancel-success]
    Verify CSS property value    &{TEMPLATE_ELEMENT}[success-alert]    background-color    'value' == '&{CSS}[alert-success-background-color]'

terminate button will stay disabled
    Verify button is disabled    &{SUBSCRIPTION_PAGE}[terminate-button]

terminate button will be disabled
    Verify button is disabled    &{SUBSCRIPTION_PAGE}[terminate-button]

terminate button will be enabled
    Verify button is enabled    &{SUBSCRIPTION_PAGE}[terminate-button]

terminate subscription page will be loaded
    Wait Until Element is Visible    &{SUBSCRIPTION_PAGE}[terminate-button]     timeout=15
    Element Text Should Be    &{TEMPLATE_ELEMENT}[success-alert]    &{ELEMENT_TEXT}[subscription-success]
    Verify CSS property value    &{TEMPLATE_ELEMENT}[success-alert]    background-color    'value' == '&{CSS}[alert-success-background-color]'

terminating warning and acknowledgement checkbox will be hidden
    Wait Until Element is Not Visible    &{TEMPLATE_ELEMENT}[danger-alert]
    Wait Until Element is Not Visible    &{SUBSCRIPTION_PAGE}[terminate-warning-checkbox]

terminating warning and acknowledgement checkbox will be shown
    Wait Until Element is Visible    &{TEMPLATE_ELEMENT}[danger-alert]
    Wait Until Element is Visible    &{SUBSCRIPTION_PAGE}[terminate-warning-checkbox]

Vastuu Group footer will be shown
    Element Should Be Visible    &{TEMPLATE_ELEMENT}[stv-footer]

tooltip will be shown
    ${element}=    Set Variable    &{TOOLTIP}[${ELEMENT}]
    ${tooltip}=    Get Element Attribute    ${element}[locator]    ${element}[attribute]
    Should Be Equal    ${tooltip}    ${MESSAGE}

user without permissions is not allowed to use BOL
    Wait until page contains        You don’t have a permission to use this service    timeout=30
    Location Should Contain         /nopermissionforservice

View latest report button will not be shown
    Page Should Not Contain    &{COMPANY_ELEMENT}[view-latest-report-button]

warning checkbox will be unchecked
    Click Element    &{SUBSCRIPTION_PAGE}[terminate-checkbox]
    verify checkbox state    &{SUBSCRIPTION_PAGE}[terminate-warning-checkbox-status]    unchecked

warning message containing "${message}" will be shown
    verify message contains    warning    ${message}    timeout=15
    Verify CSS property value    &{TEMPLATE_ELEMENT}[warning-alert]    background-color    'value' == '&{CSS}[alert-warning-background-color]'

warning message "${message}" will be shown
    Element Text Should Be    &{TEMPLATE_ELEMENT}[warning-alert]    ${message}
    Verify CSS property value    &{TEMPLATE_ELEMENT}[warning-alert]    background-color    'value' == '&{CSS}[alert-warning-background-color]'

warning will be removed
    Wait Until Element Is Not Visible    &{TEMPLATE_ELEMENT}[warning-alert]

Zeckit widget will be shown
    Wait company view is loaded
    Select Frame    &{COMPANY_ELEMENT}[zeckit]
    Wait until element is visible    &{COMPANY_ELEMENT}[zeckit-header-link]

"${country}" companies will be listed
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]     timeout=15
    verify search result    ${SEARCH_TEXT}    ${country}

"${filter}" dates ${_} date will be disabled
    Click Element    &{COMPANY_ELEMENT}[${filter}-date-filter]
    ${locator}=    handle datepicker month view    filter_type=${filter}     number_of_days=3
    Wait Until Element Is Visible    ${locator}
    Verify CSS property value    ${locator}       cursor     'value' == 'not-allowed'

"${language}" will be available
    Wait Until Element Is Visible    &{USER_DROPDOWN}[lang-${language.lower()}]
    Element Should Contain    &{USER_DROPDOWN}[lang-${language.lower()}]    ${language}

"${message}" message will be shown
    Wait Until Element Contains    &{SEARCH_PAGE}[info-message]    ${message}

"${message}" warning message will be shown
    Wait Until Element Contains    &{SEARCH_PAGE}[warning-message]    ${message}

"${page}" page will be loaded
    Wait Until Page Contains Element    &{${page.upper()}_PAGE}[page-id]    timeout=15

user sees error and no option to visit parent company
    Wait company view is loaded
    Page should Contain                 Whoops, looks like something went wrong when fetching the parent company information
    Page Should Not Contain Element     &{COMPANY_ELEMENT}[go-to-parent-button]

there should be "${number}" archived report(s) with archive id(s): "@{archive_id_list}" and status(es): "@{status_list}"
    ${id_count}=    Get length                      ${archive_id_list}
    Should be equal as integers                     ${number}                   ${id_count}

    ${status_count}=    Get length                  ${status_list}
    Should be equal as integers                     ${number}                   ${status_count}

    Wait Until Page Does Not Contain Element        &{TEMPLATE_ELEMENT}[loading-spinner]
    verify archived report details
    ...     expected_archive_id_list=${archive_id_list}
    ...     expected_status_list=${status_list}

search results show RALA certificate in "${language}"
    Wait Until Page Does Not Contain Element    &{TEMPLATE_ELEMENT}[loading-spinner]
    verify rala certificate logo is shown       &{LANG_TO_ISO}[${language}]

search results show RALA competence in "${language}"
    Wait Until Page Does Not Contain Element    &{TEMPLATE_ELEMENT}[loading-spinner]
    verify rala competence logo is shown        &{LANG_TO_ISO}[${language}]

search results do not show RALA competence logo
    Wait Until Page Does Not Contain Element    &{TEMPLATE_ELEMENT}[loading-spinner]
    Page Should Not Contain Element             &{COMPANY_ELEMENT}[rala-competence-logo]

search results do not show RALA certificate logo
    Wait Until Page Does Not Contain Element    &{TEMPLATE_ELEMENT}[loading-spinner]
    Page Should Not Contain Element             &{COMPANY_ELEMENT}[rala-certificate-logo]

last email recieved by "${username}" has template: "${template}" and contains: ${message}
    open email
    ...     email_address=${username}
    ...     expected_template=${template}
    Page should contain                         ${message}

user sees selected company has status "${status_text}"
    Wait until element is visible               &{SEARCH_HISTORY_PAGE}[company-selected-status]
    Element should contain                      &{SEARCH_HISTORY_PAGE}[company-selected-status]   ${status_text}
