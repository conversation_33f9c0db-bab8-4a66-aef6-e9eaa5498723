*** Keywords ***
Wait archived pane is loaded
    Start loading spinner
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]
#    Wait Until Element Is Visible    &{COMPANY_ELEMENT}[archived-reports-pane]
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]   timeout=15

Wait company view is loaded
    Wait Until Element Is Visible    &{COMPANY_ELEMENT}[company-view]
    Wait Until Page Does Not Contain Element     &{TEMPLATE_ELEMENT}[loading-spinner]

Verify button is enabled
    [Arguments]    ${button}
    Verify CSS property value    ${button}       cursor     'value' == 'pointer'
    Verify CSS property value    ${button}/..    opacity    0.99 < value < 1.01

Verify button is disabled
    [Arguments]    ${button}
    Verify CSS property value    ${button}       cursor     'value' == 'not-allowed'
    Verify CSS property value    ${button}/..    opacity    0.64 < value < 0.66

Verify CSS property value
    [Documentation]    ${expected} argument is condition for Should Be True keyword and in need to have text 'value'
    ...    which is replaced with the value of the property (example: 'value' == 'not-allowed')
    [Arguments]    ${locator}    ${property}    ${expected}
    ${element}=    Get WebElement    ${locator}
    Should Be True    ${expected.replace('value', '${element.value_of_css_property("${property}")}')}

Verify opened report
    [Arguments]    ${title}    ${url_contains}
    Select Window    NEW    5
    Wait For Condition    return document.title.indexOf("${title}") > -1
    Wait For Condition    return document.URL.indexOf("${url_contains}") > -1
