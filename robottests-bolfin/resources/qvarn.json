{"users": [{"set_address_and_invoicing": true, "first_name": "bolfin_1", "last_name": "robot_1", "email": "<EMAIL>", "username": "<EMAIL>", "password": "robottest", "phone": "*********", "address": "Street bolfin 1", "city": "City bolfin 1", "postal_code": "01234", "country": "FI", "org_name": "BOLFIN robot company 1", "org_number": "4542713-1", "org_invoicing_email_address": "<EMAIL>", "org_address": "Street robot company 1", "permissions": ["raportti_user"], "role": "main", "my_companies": [{"name": "Testiyritys '<PERSON><PERSON>' <PERSON>y", "archive_id": "FI232732711339406654000", "org_id": "2327327-1", "country": "FIN", "status": "OK", "language": "FI"}, {"name": "Estonian Robot Company 1", "archive_id": "EE121869551339406654005", "org_id": "12186955", "country": "EST", "status": "OK", "language": "FI"}, {"name": "Estonian Robot Company 2", "archive_id": "EE458205721339406654009", "org_id": "45820572", "country": "EST", "status": "Attention", "language": "FI"}, {"name": "Estonian Robot Firm 1", "archive_id": "EE637647271339406654007", "org_id": "63764727", "country": "EST", "status": "Incomplete", "language": "FI"}, {"name": "Estonian Robot Firm 2", "archive_id": "EE57636873201206112350160", "org_id": "57636873", "country": "EST", "status": "Investigate", "language": "FI"}, {"name": "Estonian Robot Firm 3", "archive_id": "EE23572879201206111851170", "org_id": "23572879", "country": "EST", "status": "Warning!", "language": "FI"}]}, {"set_address_and_invoicing": true, "first_name": "bolfin_1_basic", "last_name": "robot_1_basic", "email": "<EMAIL>", "username": "<EMAIL>", "password": "robottest", "phone": "*********", "address": "Street bolfin 1 basic", "city": "City bolfin 1 basic", "postal_code": "12345", "country": "FI", "org_name": "BOLFIN robot company 1", "org_number": "4542713-1", "org_invoicing_email_address": "<EMAIL>", "org_address": "Street robot company 1 basic", "permissions": ["raportti_user"], "role": "basic", "my_companies": [{"name": "Testiyritys '<PERSON><PERSON>' <PERSON>y", "archive_id": "FI232732711339406654000", "org_id": "2327327-1", "country": "FIN", "status": "OK", "language": "FI"}, {"name": "Estonian Robot Company 1", "archive_id": "EE121869551339406654005", "org_id": "12186955", "country": "EST", "status": "OK", "language": "FI"}, {"name": "Estonian Robot Company 2", "archive_id": "EE458205721339406654009", "org_id": "45820572", "country": "EST", "status": "Attention", "language": "FI"}, {"name": "Estonian Robot Firm 1", "archive_id": "EE637647271339406654007", "org_id": "63764727", "country": "EST", "status": "Incomplete", "language": "FI"}, {"name": "Estonian Robot Firm 2", "archive_id": "EE57636873201206112350160", "org_id": "57636873", "country": "EST", "status": "Investigate", "language": "FI"}, {"name": "Estonian Robot Firm 3", "archive_id": "EE23572879201206111851170", "org_id": "23572879", "country": "EST", "status": "Warning!", "language": "FI"}]}, {"set_address_and_invoicing": true, "first_name": "bolfin_1_no_permission", "last_name": "robot_1_no_permission", "email": "<EMAIL>", "username": "<EMAIL>", "password": "robottest", "phone": "*********", "address": "Street bolfin 1 no permission", "city": "City bolfin 1 no permission", "postal_code": "23456", "country": "FI", "org_name": "BOLFIN robot company 1", "org_number": "4542713-1", "org_invoicing_email_address": "<EMAIL>", "org_address": "Street robot company 1 no permission", "permissions": ["invalid"], "role": "basic"}, {"set_address_and_invoicing": false, "first_name": "bolfin_2", "last_name": "robot_2", "email": "<EMAIL>", "username": "<EMAIL>", "password": "robottest", "phone": "*********", "country": "FI", "org_name": "BOLFIN robot company 2", "org_number": "8520755-2", "org_address": "Street robot company 2", "permissions": ["raportti_user"], "role": "main"}, {"set_address_and_invoicing": true, "first_name": "bolfin_admin_1", "last_name": "robot_admin_1", "email": "<EMAIL>", "username": "<EMAIL>", "password": "robottest", "phone": "*********", "address": "Street bolfin admin 1", "city": "City bolfin admin 1", "postal_code": "43210", "country": "FI", "org_name": "BOLFIN ADMIN robot company 1", "org_number": "0242522-1", "org_invoicing_email_address": "<EMAIL>", "org_address": "Street robot company admin 1", "permissions": ["raportti_user"], "global_permissions": ["super_sp_admin"], "role": "main", "my_companies": []}], "services": [{"type": "service", "service_info": [{"service_names": [{"language": "fi_FI", "service_name": "<PERSON><PERSON><PERSON>"}, {"language": "sv_SE", "service_name": "Rapport"}, {"language": "en_GB", "service_name": "Report"}, {"language": "et_EE", "service_name": "<PERSON><PERSON>"}], "service_permissions": ["raportti_user"], "service_provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "service_url": "https://bolfin.tilaajavastuu.fi"}], "service_key": "reports_pro"}], "service_plans": [{"type": "service_plan", "dependencies": [], "service_id": "b719-2fdfff0f60359a50b9e095118a2e9a85-b4f65299", "names": [{"language": "id", "name": "report_pro"}], "pricing": [{"currency": "EUR", "invoicing_period": "12", "price": 130}]}], "performance": [{"name": "Performance 1", "archive_id": "EE200000011339406654101", "org_id": "20000001", "country": "EST", "language": "FI"}, {"name": "Performance 2", "archive_id": "EE200000021339406654102", "org_id": "20000002", "country": "EST", "language": "FI"}, {"name": "Performance 3", "archive_id": "EE200000031339406654103", "org_id": "20000003", "country": "EST", "language": "FI"}, {"name": "Performance 4", "archive_id": "EE200000041339406654104", "org_id": "20000004", "country": "EST", "language": "FI"}, {"name": "Performance 5", "archive_id": "EE200000051339406654105", "org_id": "20000005", "country": "EST", "language": "FI"}, {"name": "Performance 6", "archive_id": "EE200000061339406654106", "org_id": "20000006", "country": "EST", "language": "FI"}, {"name": "Performance 7", "archive_id": "EE200000071339406654107", "org_id": "20000007", "country": "EST", "language": "FI"}, {"name": "Performance 8", "archive_id": "EE200000081339406654108", "org_id": "20000008", "country": "EST", "language": "FI"}, {"name": "Performance 9", "archive_id": "EE200000091339406654109", "org_id": "20000009", "country": "EST", "language": "FI"}, {"name": "Performance 10", "archive_id": "EE200000101339406654110", "org_id": "20000010", "country": "EST", "language": "FI"}, {"name": "Performance 11", "archive_id": "EE200000111339406654111", "org_id": "20000011", "country": "EST", "language": "FI"}, {"name": "Performance 12", "archive_id": "EE200000121339406654112", "org_id": "20000012", "country": "EST", "language": "FI"}, {"name": "Performance 13", "archive_id": "EE200000131339406654113", "org_id": "20000013", "country": "EST", "language": "FI"}, {"name": "Performance 14", "archive_id": "EE200000141339406654114", "org_id": "20000014", "country": "EST", "language": "FI"}, {"name": "Performance 15", "archive_id": "EE200000151339406654115", "org_id": "20000015", "country": "EST", "language": "FI"}, {"name": "Performance 16", "archive_id": "EE200000161339406654116", "org_id": "20000016", "country": "EST", "language": "FI"}, {"name": "Performance 17", "archive_id": "EE200000171339406654117", "org_id": "20000017", "country": "EST", "language": "FI"}, {"name": "Performance 18", "archive_id": "EE200000181339406654118", "org_id": "20000018", "country": "EST", "language": "FI"}, {"name": "Performance 19", "archive_id": "EE200000191339406654119", "org_id": "20000019", "country": "EST", "language": "FI"}, {"name": "Performance 20", "archive_id": "EE200000201339406654120", "org_id": "20000020", "country": "EST", "language": "FI"}]}