#!/bin/bash

cd "$(dirname "$0")" || exit 1

robot_location=$(which robot) || robot_location=env/bin/robot

./robot \
  -P . \
  --outputdir output \
  --debugfile debug.log \
  -e not_ready \
  -n open_issue \
  -L TRACE \
  -v BROWSER:firefox \
  -v BASE_URL:"http://localhost:8080" \
  -v QVARN_URL:"http://localhost:6080" \
  -v SP_BASE_URL:"http://localhost:8081" \
  --listener listeners/listener.py \
  -C ansi \
  --robot-script $robot_location \
  --retry=0 \
  "$@" \
