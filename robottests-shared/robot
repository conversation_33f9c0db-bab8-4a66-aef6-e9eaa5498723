#!/usr/bin/env python3
"""
Robot framework wrapper that can retry failed tests.

Inspired by
http://laurent.bristiel.com/re-executing-failed-test-cases-and-merging-outputs-with-robot-framework/

Over time it has acquired some additional features.
"""

import argparse
import atexit
import collections
import json
import os
import pathlib
import shlex
import shutil
import signal
import subprocess
import sys
import textwrap
import urllib.parse
import urllib.request


# NB: important to use absolute() because we chdir()
here = pathlib.Path(__file__).parent.absolute()


BASE_URL = os.environ.get('BF_BASE_URL', 'http://localhost:8080')


def debug(msg):
    """Perhaps print a debug message."""
    # uncomment while debugging
    # print(msg)


CompletedProcess = collections.namedtuple('CompletedProcess',
                                          ('args', 'returncode', 'stdout', 'stderr'))


def run(args, *popenargs, check=False, **kwargs):
    # Back-ported subprocess.run from python3.5.

    print('\nrun: %s' % ' '.join(map(shlex.quote, args)), flush=True)

    with subprocess.Popen(args, *popenargs, **kwargs) as process:
        try:
            stdout, stderr = process.communicate()
        except KeyboardInterrupt:
            process.send_signal(signal.SIGINT)
            process.wait()
            raise
        except Exception:
            try:
                process.terminate()
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
            raise

        retcode = process.poll()

        if check and retcode:
            raise subprocess.CalledProcessError(retcode, process.args, output=stdout, stderr=stderr)

    return CompletedProcess(process.args, retcode, stdout, stderr)


def runrobot(robot, outdir, argv, tests_location, real_directory=None):
    args = [
        str(robot),
        '--outputdir', str(outdir),
        '--debugfile', 'debug.log',
        '--pythonpath', 'lib',
    ]

    if sys.stdout.isatty() and os.environ.get('TERM', 'dumb') != 'dumb' and '-C' not in argv:
        args += ['-C', 'ansi']

    # Pipe output through sed to convert log file paths into handy convenient
    # clickable file:// links
    if sys.platform == 'darwin':
        sed_buffering = '-l'  # BSD sed uses -l for line-buffered output
    else:
        sed_buffering = '-u'  # GNU sed uses -u for unbuffered output

    if real_directory:
        sed_command = r's/\/robottests/file:\/\/%s/' % (sed_escape(real_directory))
    else:
        sed_command = r's/\/home/<USER>/\/\/home/'
    outfilter = subprocess.Popen(
        ['sed', sed_buffering, '-e', sed_command],
        stdin=subprocess.PIPE,
    )
    return run(args + argv + [tests_location], stdout=outfilter.stdin).returncode


def sed_escape(replacement_text):
    return replacement_text.replace('\\', r'\\').replace('/', r'\/')


def copyrun(source: pathlib.Path, target: pathlib.Path):
    if not target.exists():
        target.mkdir()

    shutil.copy(str(source / 'log.html'), str(target / 'log.html'))
    shutil.copy(str(source / 'report.html'), str(target / 'report.html'))
    shutil.copy(str(source / 'debug.log'), str(target / 'debug.log'))
    shutil.copy(str(source / 'output.xml'), str(target / 'output.xml'))

    # Copy all the screenshots.
    # (XXX wouldn't it be better to _move_ them?)
    for screenshot in source.glob('*.png'):
        shutil.copy(str(screenshot), str(target / screenshot.name))


def rotatedir(dirname, keep):
    dirname = pathlib.Path(dirname)
    if keep > 0:
        # delete dirname.N
        # rename dirname.(N-1) to dirname.N
        # rename dirname.(N-2) to dirname.(N-1)
        # ...
        # rename dirname to dirname.1
        while True:
            dirname_last = dirname.with_suffix('.{}'.format(keep))
            if not dirname_last.exists():
                break
            debug('removing %s' % dirname_last)
            try:
                shutil.rmtree(str(dirname_last))
            except OSError as e:
                debug('failed to remove %s: %s' % (dirname_last, e))
                keep += 1
            else:
                break
        for n in range(keep, 1, -1):
            dirname_n1 = dirname.with_suffix('.{}'.format(n - 1))
            if dirname_n1.exists():
                dirname_n = dirname.with_suffix('.{}'.format(n))
                debug('renaming %s -> %s' % (dirname_n1, dirname_n))
                dirname_n1.rename(dirname_n)
        if dirname.exists():
            dirname_1 = dirname.with_suffix('.1')
            debug('renaming %s -> %s' % (dirname, dirname_1))
            dirname.rename(dirname_1)
    else:
        debug('removing %s' % dirname)
        shutil.rmtree(str(dirname))


def relativize_symlinks(where):
    # (It would be easier to run(["symlinks", "-c", "-r", str(where)]), but
    # symlinks is not packaged for Alpine.  Ironically, this script runs in a
    # Debian container, and not Alpine at all, so I wrote this code for nothing!)
    for dirpath, dirnames, filenames in os.walk(str(where)):
        for fn in filenames:
            fullfn = os.path.join(dirpath, fn)
            if not os.path.islink(fullfn):
                continue
            target = os.readlink(fullfn)
            reltarget = os.path.relpath(os.path.join(dirpath, target), dirpath)
            if reltarget != target:
                debug('replacing %s -> %s with -> %s' % (fullfn, target, reltarget))
                os.unlink(fullfn)
                os.symlink(reltarget, fullfn)


def deduplicate(where):
    # We create a lot of identical screenshots (hundreds of megabytes), so
    # let's replace them with symlinks!  One important reason to do this is
    # to avoid GitLab CI artifact size limits (which is somewhere upwards of
    # 100 MB).
    # There are several tools (jdupes, rdfind, hardlinks, ok, maybe not that
    # last one) that can do this for us, but only rdfind is packaged for Alpine
    # v3.8.  Ironically, this script runs in a Debian container, and not Alpine
    # at all.
    if shutil.which("jdupes"):
        # recursive, make symlinks, be quiet
        run(["jdupes", "-r", "-l", "-q", str(where)])
    elif shutil.which("rdfind"):
        run(["rdfind", "-makesymlinks", "true", str(where)])
    else:
        print("neither jdupes nor rdfind found, skipping deduplication")
        return
    relativize_symlinks(where)


def check_docker_tag(expected):
    url = urllib.parse.urljoin(BASE_URL, '/api/version?recursive=no')
    with urllib.request.urlopen(url) as fp:
        version_info = json.loads(fp.read().decode('UTF-8'))
    if expected == 'latest':
        print("{} returned docker_tag of {}, which I hope is latest".format(
            url, version_info['docker_tag']))
    elif version_info['docker_tag'] != expected:
        raise AssertionError('{} returned docker_tag of {}, expected {}'.format(
            url, version_info['docker_tag'], expected))
    else:
        print("{} returned expected docker_tag of {}".format(url, expected))


def nicepath(path):
    path = str(path)  # in case it was a pathlib.Path
    alt = os.path.relpath(path)
    if len(alt) < len(path):
        path = alt
    return path


def main(argv=None):
    # This script is partially based on
    # http://laurent.bristiel.com/re-executing-failed-test-cases-and-merging-outputs-with-robot-framework/

    default_robot_script = nicepath(here / 'env' / 'bin' / 'robot')

    parser = argparse.ArgumentParser(
        add_help=False,
        formatter_class=argparse.RawDescriptionHelpFormatter,
        description=textwrap.dedent('''\
            This is the {robot} command wrapper.

            Currently this script mostly sets some defauls for {robot} command
            and adds the following features:

            Chromedriver location
            ---------------------

            If /usr/lib/chromium-browser/chromedriver, or /usr/lib/chromium/chromedriver,
            or ./env/bin/chromedriver exist, the corresponding directory is added to $PATH

            Docker tag verification
            -----------------------

            If --check-docker-tag=12345 is provided on the command line, the wrapper script
            will attempt to parse $BF_BASE_URL/api/version as JSON and check that it has
            docker_tag: "12345".  ($BF_BASE_URL defaults to http://localhost:8080)

            Failed test retries
            -------------------

            If the --retry option is specified, after running tests for the
            first time, if some tests failed, run robot tests again, but only
            run the failed tess.

            If more than one retry time is specified (--retry=N), repeat
            retries the specified number of times. Each retry will repeat only
            those tests which failed the previous time (not the first time).

            After each retry one output.html file will be generated containing
            information about all test runs, including all retries.

            The idea is borrowed from:

            http://laurent.bristiel.com/re-executing-failed-test-cases-and-merging-outputs-with-robot-framework/

            Screenshot deduplication
            ------------------------

            Long runs with many failures produce many screenshots.  Sometimes these take
            up too much disk space that GitLab CI refuses to archive them as artifacts.
            Luckily, often the screenshots are identical and can be replaced by symlinks.
            (This wrapper invokes jdupes or rdfind to do the actual deduplication.)

        '''.format(robot=default_robot_script)),
    )
    parser.add_argument(
        '-h', '--help',
        action='store_true',
        help="Show this help message",
    )
    parser.add_argument(
        "--outdir",
        default="output",
        help="Output directory for logs and reports",
    )
    parser.add_argument(
        "--envdir",
        default=nicepath(here / "env"),
        help="Pathname of the virtualenv that might contain chromedriver (default: %(default)s)",
    )
    parser.add_argument(
        "--robot-script",
        help="Pathname of the original robot script (default: bin/robot in --envdir)",
    )
    parser.add_argument(
        "--rebot-script",
        help=(
            "Pathname of the original rebot script (default: rebot in the same directory"
            " as --robot-script)"
        ),
    )
    parser.add_argument('--check-docker-tag', metavar='TAG',
                        help='Make sure the docker_tag in /api/version is the right one')
    parser.add_argument('--browser',
                        help='Set BF_BROWSER (e.g. chrome/firefox/headlesschrome/headlessfirefox)')
    parser.add_argument('--headless', action='store_const', dest='browser', const='headlesschrome',
                        help='Set BF_BROWSER to headlesschrome')
    parser.add_argument('--xephyr', action='store_true', default=False,
                        help='Run the browser inside a nested X11 server to prevent focus stealing')
    parser.add_argument('--retry', type=int, metavar='TIMES', nargs='?', const=1, default=0,
                        help='Retry failed tests the specified number of times. If '
                             'TIMES is not specified, retry once.')
    parser.add_argument('--no-retry', '--no-retries', action='store_const', const=0, dest='retry',
                        help='Do not retry failed tests (default)')
    parser.add_argument('--keep', type=int, metavar='N', default=6,
                        help='Keep N copies of the output directory of older runs'
                             ' (default: %(default)s)')
    parser.add_argument('--tests-location', default='tests',
                        help='Custom name of dir where robot tests are located'
                             ' (default: %(default)s)')
    parser.add_argument('--real-directory', metavar='DIR',
                        help="Rewrite /robottests/ to file://DIR in stdout,"
                             " for when you're running in Docker with a mounted volume"
                             " (default: assume you're running outside docker)")
    args, argv = parser.parse_known_args(argv)

    # important to convert paths to absolute before we os.chdir()
    envdir = pathlib.Path(args.envdir).resolve()
    robot = pathlib.Path(args.robot_script).resolve() if args.robot_script else (
        envdir / 'bin' / 'robot'
    )
    rebot = pathlib.Path(args.rebot_script).resolve() if args.rebot_script else (
        robot.parent / 'rebot'
    )

    if args.help:
        parser.print_help()
        print('\n' + '-' * 72 + '\n', flush=True)
        print('Any other arguments and options are passed directly to the robot framework runner.')
        print('Run {robot} --help to get help about those.'.format(robot=nicepath(robot)))
        return

    if args.check_docker_tag:
        check_docker_tag(args.check_docker_tag)

    if args.browser:
        print('Overriding BF_BROWSER=%s' % args.browser)
        os.environ['BF_BROWSER'] = args.browser

    if args.tests_location:
        tests_location = args.tests_location
    else:
        tests_location = 'tests'

    print('Will be looking for robots cases in %s/ folder.' % tests_location)

    if args.real_directory:
        args.real_directory = os.path.abspath(args.real_directory)

    os.chdir(str(here))

    # Check if the robottest framework has been installed, alert if not
    # May happen since this script is often run outside make.
    if not robot.exists():
        print("%s missing. Perhaps run 'make'?" % robot, file=sys.stderr)
        return 1

    robot = nicepath(robot)
    rebot = nicepath(rebot)

    # Check if chromedriver is in PATH, if not, try to
    # default Ubuntu and Debian locations to PATH.
    if not shutil.which('chromedriver'):
        chromedriver_locations = (
            envdir / 'bin',
            '/usr/lib/chromium-browser',
            '/usr/lib/chromium',
        )
        for location in map(pathlib.Path, chromedriver_locations):
            if location.exists():
                os.environ['PATH'] += ':%s' % location
                print("Adding %s to PATH" % location)

    outdir = pathlib.Path(args.outdir)

    # Rotate output directory
    rotatedir(outdir, args.keep)

    if args.xephyr:
        xephyr_size = os.environ.get('XEPHYR_SIZE', '1300x720')
        xephyr = subprocess.Popen(['Xephyr', ':2', '-screen', xephyr_size],
                                  stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        os.environ['DISPLAY'] = ':2'
        os.environ.pop('WAYLAND_DISPLAY', None)
        atexit.register(xephyr.terminate)

    # Run robot tests.
    retcode = runrobot(robot,
                       outdir,
                       argv,
                       tests_location=tests_location,
                       real_directory=args.real_directory)

    # Exit if tests pass otherwise continue if --retry flag is set.
    success = 0
    help_or_version = 251
    if retcode in (success, help_or_version) or not args.retry:
        deduplicate(outdir)
        return retcode

    if not outdir.exists():
        # This happens when the user passes an unknown option: the robot script exits
        # with an error code without producing any log output.
        return retcode

    # Keep a copy of the first run log file.
    copyrun(outdir, outdir / 'run-0')

    # For all our reruns we will use errors from rerun instead of errors from
    # all merged outputs.
    shutil.copy(str(outdir / 'output.xml'), str(outdir / 'rerun.xml'))

    # Rerun *only* failed tests the specified number of times.
    for i in range(1, args.retry + 1):

        print('\nretrying (%d/%d)' % (i, args.retry))

        # Rerun only failed robot tests and write output into rerun.xml.
        retcode = runrobot(robot, outdir, argv + [
            '--rerunfailedsuites', str(outdir / 'rerun.xml'),
            '--output', 'rerun.xml',
        ], tests_location=tests_location, real_directory=args.real_directory)

        # Keep a copy of the second run log file.
        copyrun(outdir, outdir / ('run-%d' % i))

        # Then merge output.xml with rerun.xml and write merged output into
        # output.xml.
        run([
            rebot,
            '--outputdir', str(outdir),
            '--output', 'output.xml',
            '--merge', str(outdir / 'output.xml'),
            str(outdir / 'rerun.xml'),
        ])

        # Stop retrying if tests pass.
        if retcode == 0:
            break

    deduplicate(outdir)
    return retcode


if __name__ == "__main__":
    sys.exit(main() or 0)
